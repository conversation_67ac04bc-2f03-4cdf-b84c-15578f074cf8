﻿import { EventEmitter } from '../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { ButtonHelper } from '../../../../../../helper/button-helper.js?v=#{BuildVersion}#'
import { SystemDocument } from "../../../../../../config/system-document-enum.js?v=#{BuildVersion}#";
export class PurchaseOrderMainInfoManager extends EventEmitter{
    constructor(purchaseOrderId) {
        super();
        this.purchaseOrderId = purchaseOrderId;
        this.$purchaseOrdersMainInfoWrapper = $('#purchase-orders-main-info-wrapper');
        this.$purchaseOrdersMainInfoSectionBox = $('#purchase-orders-main-information-box');
        this.poMainInfo = null;

    }

    async initialize() {
        this.setupSectionBox();
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", true);

        await this.getMainInfo();
        this.bindMainInfo();
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", false);

    }

    setupSectionBox() {
        this.$purchaseOrdersMainInfoSectionBox.section_box({
            onRefreshClick: async () => {
                await this.refreshSectionBox();
            }
        });
    }

    async getMainInfo() {
        const response = await GlobalTrader.ApiClient.getAsync(`/orders/purchase-orders/details/${this.purchaseOrderId}/main-info`)
        if (response.success) {
            this.poMainInfo = response.data
        }
        else {
            console.log(response.error);
        }
    }

    bindMainInfo() {
        const htmlRawFieldNames = [
            "notes",
            "instructions"
        ];
        const supplierButtonHtml = ButtonHelper.nubButton_Company(this.poMainInfo.supplierNo, this.poMainInfo.supplierName, this.poMainInfo.supplierAdvisoryNotes)
        this.$purchaseOrdersMainInfoSectionBox.find(`#purchase-order-form-supplier-name`).html(supplierButtonHtml);
        if (this.poMainInfo.internalPurchaseOrderNo > 0) {
            $("#ipo-field").show();
            const ipoButton = ButtonHelper.createNubButton(ButtonHelper.URL_InternalPurchaseOrder(this.poMainInfo.internalPurchaseOrderNo), this.poMainInfo.internalPurchaseOrderNumber)
            $("#purchase-order-form-ipo-no").html(ipoButton)
        }

        const contactButtonHtml = ButtonHelper.nubButton_Contact(this.poMainInfo.contactNo, this.poMainInfo.contactName);
        this.$purchaseOrdersMainInfoSectionBox.find(`#purchase-order-form-contact`).html(contactButtonHtml);
        this.$purchaseOrdersMainInfoSectionBox.find(`#purchase-order-form-supplier-website`)
            .attr("href", GlobalTrader.StringHelper.formatURL(this.poMainInfo.websiteAddress))
            .text(this.poMainInfo.websiteAddress)
        const as6081text = (this.poMainInfo.aS6081 ? window.localizedStrings.yes : window.localizedStrings.no)
        this.$purchaseOrdersMainInfoSectionBox.find("#aS6081-included")
            .text(as6081text)
            .css("background-color", this.poMainInfo.aS6081 ? "yellow" : "")

        const srmaIds = this.formatSRMA()
        const debitIds = this.formatDebit()
 
        if (srmaIds.length > 0) {
            this.$purchaseOrdersMainInfoSectionBox.find("#srma").html(srmaIds)
            this.$purchaseOrdersMainInfoSectionBox.find("#srma-field").show()
        }
        if (debitIds.length > 0) {
            this.$purchaseOrdersMainInfoSectionBox.find("#debit").html(debitIds)
            this.$purchaseOrdersMainInfoSectionBox.find("#debit-field").show()
        }
        
        this.$purchaseOrdersMainInfoWrapper.find("input[type=checkbox]").toArray().forEach(input => {
            const fieldName = $(input).data("field");
            $(input).prop("checked", this.getPropertyCaseInsensitive(this.poMainInfo, fieldName))
                .trigger('change');
        });

        this.$purchaseOrdersMainInfoWrapper.find("span[data-field]").toArray().forEach(element => {
            const fieldName = $(element).data("field");
            let fieldValue = this.getPropertyCaseInsensitive(this.poMainInfo, fieldName);

            if (htmlRawFieldNames.includes(fieldName)) {
                $(element).html(GlobalTrader.StringHelper.setCleanTextValue(fieldValue, true));
            } else {
                $(element).text(GlobalTrader.StringHelper.setCleanTextValue(fieldValue));

            }
        });
        this.setWarningMessages();

    }

    async setWarningMessages() {
        if (this.poMainInfo.isHighRisk) {
            $("#risk-warning-section").removeClass("d-none").show()
                .find("*").removeClass("d-none").show();
            $("#risk-text").text(this.poMainInfo.warningMessage);
            $("#ship-from-icon")
                .show()
                .attr("title", GlobalTrader.StringHelper.setCleanTextValue(this.poMainInfo.warningMessage))
                .attr("src", "/img/icons/circle-info-yellow.svg");
        }
        else {
            $("#risk-warning-section")
                .html(``)
                .hide()
        }

        if (this.poMainInfo.isHasCountryMessage) {
            $("#ship-from-icon")
                .show()
                .attr("title", GlobalTrader.StringHelper.setCleanTextValue(this.poMainInfo.countryWarningMessage))
                .attr("src", "/img/hazardous/Hazardousone.png");
        }


        let isSanctioned = (this.poMainInfo.isPOHub) ? this.PoMainInfo.isHighRisk : this.poMainInfo.isSanctioned
        if (isSanctioned) {
            $("#sanction-warning-section").removeClass("d-none").show()
                .find("*").removeClass("d-none").show()
            $("#sanction-text").text(localizedStrings.sanctionMessage)
    
        }
        else {
            $("#sanction-warning-section")
                .text("")
                .hide()
        }

        if (this.poMainInfo.supplierMessage) {
            // ERAI
            $("#erai-icon")
                .show()
                //.attr("title", GlobalTrader.StringHelper.setCleanTextValue(this.poMainInfo.supplierMessage))
            $("#supplier-value-field").css("background-color", "yellow");
        }
    }

    formatSRMA() {
        let srmaId = ``;
        if ((this.poMainInfo.supplierRMAIds) && (this.poMainInfo.supplierRMANumbers)) {
            for (let i = 0; i < this.poMainInfo.supplierRMANumbers.length; i++) {
                let row = this.poMainInfo.supplierRMAIds[i];
                let row1 = this.poMainInfo.supplierRMANumbers[i];
                if (i != 0) srmaId += `, `
                srmaId += ButtonHelper.nubButton_SystemDocument(SystemDocument.SupplierRMA, row.SupplierRMAId, row1.SupplierRMANumber);
            }
        }
        return srmaId
    }

    formatDebit() {
        let debitId = ``;
        if ((this.poMainInfo.debitIds) && (this.poMainInfo.debitNumbers)) {
            for (let i = 0; i < this.poMainInfo.debitNumbers.length; i++) {
                let row = this.poMainInfo.debitIds[i];
                let row1 = this.poMainInfo.debitNumbers[i];
                if (i != 0) debitId += `, `
                debitId += ButtonHelper.nubButton_SystemDocument(SystemDocument.DebitNote, row.debitId, row1.debitNumber);
            }
        }
        return debitId
    }
    
    // Get value by property name, case insensitive
    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }

    async refreshSectionBox() {
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", true);
        await this.getMainInfo();
        this.bindMainInfo()
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", false);
    }
}