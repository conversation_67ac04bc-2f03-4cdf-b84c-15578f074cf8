﻿import { loadCSS } from '../../helper/load-css.helper.js?v=#{BuildVersion}#';
import { EventEmitter } from '../base/event-emmiter.js?v=#{BuildVersion}#';

export class StepperComponent extends EventEmitter {
    constructor(container, steps) {
        super();
        this.container = container;
        this.steps = steps;
        this.currentStep = 1;

        this.render();
        this.updateStepper(this.currentStep);
        loadCSS('/js/components/stepper/stepper.css');
        // Event Delegation for Click Handling
        this.container.addEventListener("click", (event) => {
            const stepElement = event.target.closest(".step-arrow"); // Fix: target the correct wrapper
            if (stepElement) {
                const step = parseInt(stepElement.dataset.step);
                const stepData = this.steps[step - 1];
                this.onClickStep(stepData.clickToMove, step);
            }
        });

    }

    render() {
        const nav = document.createElement('div');
        nav.className = 'stepper-horizontal';
        nav.innerHTML = this.steps.map((step, index) => `
        <div class="step-arrow ${index > 0 ? 'chained' : ''} ${index + 1 < this.currentStep ? 'completed' : ''} ${index + 1 === this.currentStep ? 'active' : ''}" data-step="${index + 1}">
            <span class="step-index-circle">
                ${index + 1 < this.currentStep ? '&#10003;' : index + 1}
            </span>
            <span class="step-title">${step.title}</span>
        </div>
        `).join('');
        this.container.prepend(nav);
    }


    updateStepper(step) {
        this.currentStep = step;

        const steps = document.querySelectorAll(".step-arrow");
        const panes = document.querySelectorAll(".step-pane");
        steps.forEach((step, index) => {
            const indexCircle = step.querySelector(".step-index-circle");
            const stepData = this.steps[index];

            if (index < this.currentStep - 1) {
                step.classList.add("completed");
                step.classList.remove("active");

                if (indexCircle) indexCircle.innerHTML = "&#10003;"; // checkmark
                this.updateAllowClick(step, stepData);
            } else if (index === this.currentStep - 1) {
                step.classList.add("active");
                step.classList.remove("completed");
                step.classList.remove("allow-click"); // always remove for active step

                if (indexCircle) indexCircle.innerHTML = index + 1; // current step number
            } else {
                step.classList.remove("completed", "active");

                if (indexCircle) indexCircle.innerHTML = index + 1; // reset number
                this.updateAllowClick(step, stepData);
            }
        });

        

        panes.forEach((pane, index) => {
            pane.classList.toggle("d-none", index !== this.currentStep - 1);
            $(pane).find(":focusable").first().trigger("focus");
        });

        this.trigger("stepChange.ms", { step: this.currentStep });
    }

    onNextStep() {
        this.currentStep++;
        this.updateStepper(this.currentStep);
    }

    onPreviousStep() {
        this.currentStep--;
        this.updateStepper(this.currentStep);
    }

    onClickStep(clickToMove, step) {
        // Allow moving only backward, not forward
        if (!clickToMove || step >= this.currentStep) {
            return;
        }
        this.currentStep = step;
        this.updateStepper(this.currentStep);
    }

    updateAllowClick(stepEl, stepData) {
        if (stepData.clickToMove) {
            stepEl.classList.add("allow-click");
        } else {
            stepEl.classList.remove("allow-click");
        }
    }
}