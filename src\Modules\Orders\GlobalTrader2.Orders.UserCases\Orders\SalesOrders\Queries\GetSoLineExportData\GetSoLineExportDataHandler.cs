﻿using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Dto.SalesOrder;

namespace GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSoLineExportData
{
    public class GetSoLineExportDataHandler : IRequestHandler<GetSoLineExportDataQuery, BaseResponse<IEnumerable<SalesOrderLineExportDto>>>
    {
        private readonly IBaseRepository<SalesOrderLineExportReadModel> _repo;
        private readonly IMapper _mapper;

        public GetSoLineExportDataHandler(IBaseRepository<SalesOrderLineExportReadModel> repo, IMapper mapper)
        {
            _repo = repo;
            _mapper = mapper;
        }

        public async Task<BaseResponse<IEnumerable<SalesOrderLineExportDto>>> Handle(GetSoLineExportDataQuery query, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IEnumerable<SalesOrderLineExportDto>>();
            var viewLevel = (ViewLevelList)query.Data.ViewLevel;

            var filterClientId = query.Data.CurrentClient;

            bool? isAS6081 = null;

            if (query.Filters?.AS6081 == 1)
            {
                isAS6081 = true;
            }
            else if (query.Filters?.AS6081 == 2)
            {
                isAS6081 = false;
            }

            var parameters = new List<SqlParameter>
            {
                new("@ClientId", SqlDbType.Int){Value = filterClientId ?? (object)DBNull.Value},
                new("@TeamId", SqlDbType.Int){Value = viewLevel == ViewLevelList.Team && query.Data.TeamId.HasValue ? query.Data.TeamId : DBNull.Value},
                new("@DivisionId", SqlDbType.Int){Value = viewLevel == ViewLevelList.Division && query.Data.DivisionId.HasValue ? query.Data.DivisionId : DBNull.Value},
                new("@LoginId", SqlDbType.Int){Value = viewLevel == ViewLevelList.My && query.Data.LoginId.HasValue ? query.Data.LoginId : DBNull.Value},
                new("@OrderBy", SqlDbType.Int){Value =  query.Data.OrderBy},
                new("@SortDir", SqlDbType.Int){Value =  query.Data.SortDir},
                new("@PartSearch", SqlDbType.NVarChar){Value =  query.Filters?.Part ?? (object)DBNull.Value},
                new("@ContactSearch", SqlDbType.NVarChar){Value =  query.Filters?.Contact ?? (object)DBNull.Value},
                new("@CountrySearch", SqlDbType.Int){Value = query.Filters?.Country ?? (object)DBNull.Value},
                new("@CMSearch", SqlDbType.NVarChar){Value = query.Filters?.CMName ?? (object)DBNull.Value},
                new("@SalesmanNo", SqlDbType.Int){Value =  query.Filters?.Salesman ?? (object)DBNull.Value},
                new("@CustomerPOSearch", SqlDbType.NVarChar){Value =  query.Filters?.CustPO ?? (object)DBNull.Value},
                new("@RecentOnly", SqlDbType.Bit){Value =  query.Filters?.RecentOnly ?? (object)DBNull.Value},
                new("@IncludeClosed", SqlDbType.Bit){Value =  query.Filters?.IncludeClosed ?? (object)DBNull.Value},
                new("@SalesOrderNoLo", SqlDbType.Int){Value =  query.Filters?.SONoLo ?? (object)DBNull.Value},
                new("@SalesOrderNoHi", SqlDbType.Int){Value =  query.Filters?.SONoHi ?? (object)DBNull.Value},
                new("@DateOrderedFrom", SqlDbType.DateTime){Value =  query.Filters?.DateOrderedFrom ?? (object)DBNull.Value},
                new("@DateOrderedTo", SqlDbType.DateTime){Value =  query.Filters?.DateOrderedTo ?? (object)DBNull.Value},
                new("@DatePromisedFrom", SqlDbType.DateTime){Value =  query.Filters?.DatePromisedFrom ?? (object)DBNull.Value},
                new("@DatePromisedTo", SqlDbType.DateTime){Value =  query.Filters?.DatePromisedTo ?? (object)DBNull.Value},
                new("@ContractNo", SqlDbType.NVarChar){Value =  query.Filters?.ContractNo ?? (object)DBNull.Value},
                new("@IsGlobalLogin", SqlDbType.Bit){Value =  query.Data.IsGlobalLogin ?? (object)DBNull.Value},
                new("@ClientSearch", SqlDbType.Int){Value =  query.Filters?.ClientName ?? (object)DBNull.Value},
                new("@SOCheckedStatus", SqlDbType.Int){Value =  query.Filters?.SOCheckedStatus ?? (object)DBNull.Value},
                new("@SOStatus", SqlDbType.Int){Value =  query.Filters?.SalesOrderStatus ?? (object)DBNull.Value},
                new("@AS6081", SqlDbType.Bit){Value =  isAS6081 ?? (object)DBNull.Value},
                new("@SelectedLoginId", SqlDbType.Int){Value =  query.Data.SelectedLogin ?? (object)DBNull.Value},
            };

            var salesOrders = await _repo.SqlQueryRawAsync($"{StoredProcedures.ExportSalesOrderLine} {string.Join(", ", parameters.Select(x => x.ParameterName))}",
                parameters.ToArray(),
                CommandTimeout.Timeout180Seconds
            );

            response.Success = true;
            response.Data = _mapper.Map<IEnumerable<SalesOrderLineExportDto>>(salesOrders);

            return response;
        }
    }
}
