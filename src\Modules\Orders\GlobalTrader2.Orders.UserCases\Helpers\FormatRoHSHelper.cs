﻿using GlobalTrader2.Dto.PurchaseRequisition;

namespace GlobalTrader2.Orders.UserCases.Helpers
{
    public static class FormatRoHSHelper
    {
        public static FormatRoHSDto FormatRoHS(byte? rohs)
        {
            if (!string.IsNullOrEmpty(rohs.ToString()))
            {
                switch (rohs)
                {
                    case 1: { return new FormatRoHSDto() { ROHS = "RoHS Compliant", ImageUrl = "/img/rohs/compliant.gif" }; }
                    case 2: { return new FormatRoHSDto() { ROHS = "RoHS Non-Compliant", ImageUrl = "/img/rohs/non_compliant.gif" }; }
                    case 3: { return new FormatRoHSDto() { ROHS = "RoHS Exempt", ImageUrl = "/img/rohs/exempt.gif" }; }
                    case 4: { return new FormatRoHSDto() { ROHS = "RoHS Not Applicable", ImageUrl = "" }; }
                    case 5: { return new FormatRoHSDto() { ROHS = "RoHS 2", ImageUrl = "/img/rohs/ROHS2.gif" }; }
                    case 6: { return new FormatRoHSDto() { ROHS = "RoHS 5/6", ImageUrl = "/img/rohs/ROHS56.gif" }; }
                    case 7: { return new FormatRoHSDto() { ROHS = "RoHS 6/6", ImageUrl = "/img/rohs/ROHS66.gif" }; }
                    default: { return new FormatRoHSDto() { ROHS = "RoHS Unknown", ImageUrl = "" }; }
                }
            }
            return new FormatRoHSDto() { ROHS = "", ImageUrl = "" };

        }
        public static string FormatRoHSForReport(string partNo, byte? rohs)
        {
            string textformat = "{0} ({1})";
            string rohsText = string.Empty;
            if (!string.IsNullOrEmpty(rohs.ToString()))
            {
                switch (rohs)
                {
                    case 1: { rohsText = "RoHS Compliant"; break; }
                    case 2: { rohsText = "RoHS Non-Compliant"; break; }
                    case 3: { rohsText = "RoHS Exempt"; break; }
                    case 4: { rohsText = "RoHS Not Applicable"; break; }
                    case 5: { rohsText = "RoHS 2"; break; }
                    case 6: { rohsText = "RoHS 5/6"; break; }
                    case 7: { rohsText = "RoHS 6/6"; break; }
                    default: { rohsText = "RoHS Unknown"; break; }
                }
                return string.Format(textformat, partNo ?? string.Empty, rohsText);
            }
            return partNo;


        }
    }
}
