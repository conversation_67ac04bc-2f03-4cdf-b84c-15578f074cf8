﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportManualStock;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Queries
{
    public class GetSalesOrderLineReportManualStockHandlerTest
    {
        private readonly Mock<IBaseRepository<SalesOrderLineReportManualStockReadModel>> _salesOrderLinePoRepository;
        private readonly Mock<IMapper> _mapper;
        private readonly GetSalesOrderLineReportManualStockHandler _handler;
        private readonly IFixture _fixture = new Fixture();
        public GetSalesOrderLineReportManualStockHandlerTest()
        {
            _salesOrderLinePoRepository = new Mock<IBaseRepository<SalesOrderLineReportManualStockReadModel>>();
            _mapper = new Mock<IMapper>();
            _handler = new GetSalesOrderLineReportManualStockHandler(_salesOrderLinePoRepository.Object, _mapper.Object);
        }
        [Fact]
        public async Task GetSalesOrderLineReportManualStockHandler_ReturnValueTest()
        {
            _salesOrderLinePoRepository.Setup(z => z.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>())).ReturnsUsingFixture(_fixture);
            _mapper.Setup(z=>z.Map<IReadOnlyList<SalesOrderLineReportManualStockDto>>(It.IsAny<IReadOnlyList<SalesOrderLineReportManualStockReadModel>>()))
                .ReturnsUsingFixture(_fixture);
            var request = _fixture.Create<GetSalesOrderLineReportManualStockQuery>();
            var result = await _handler.Handle(request, CancellationToken.None);
            Assert.NotNull(result);
            Assert.True(result.Success);
        }
    }
}
