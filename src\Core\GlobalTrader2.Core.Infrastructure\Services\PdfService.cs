﻿using GlobalTrader2.Core.Interfaces;

namespace GlobalTrader2.Core.Infrastructure.Services
{
    public class PdfService : IPdfService
    {
        public async Task<byte[]> CreateFromHtmlAsync(string html)
        {
            var renderer = new ChromePdfRenderer();
            PdfDocument doc = await renderer.RenderHtmlAsPdfAsync(html);
            return doc.Stream.ToArray();
        }

        public async Task<byte[]> CreateFromPdfAsync(string html, object options)
        {
            var renderer = new ChromePdfRenderer();
            if (options is ChromePdfRenderOptions option)
            {
                renderer.RenderingOptions = option;
            }
            PdfDocument doc = await renderer.RenderHtmlAsPdfAsync(html);
            return doc.Stream.ToArray();
        }

        public async Task<byte[]> CreateFromPdfAsync(string[] html, object options)
        {
            if (html.Length == 0) return [];
            if (html.Length == 1) return await CreateFromPdfAsync(html[0], options);
            var renderer = new ChromePdfRenderer();
            if (options is ChromePdfRenderOptions option)
            {
                renderer.RenderingOptions = option;
            }
            PdfDocument mainDocument = await renderer.RenderHtmlAsPdfAsync(html[0]);
            for (int i = 1; i < html.Length; i++)
            {
                var nextContent = await renderer.RenderHtmlAsPdfAsync(html[i]);
                mainDocument.InsertPdf(nextContent, i);
            }
            return mainDocument.Stream.ToArray();
        }

        public async Task<byte[]> CreateSOReportFromPdfAsync(string html)
        {
            var renderer = new ChromePdfRenderer();
            var option = new ChromePdfRenderOptions
            {
                PaperSize = IronPdf.Rendering.PdfPaperSize.A4,
                PaperOrientation = IronPdf.Rendering.PdfPaperOrientation.Portrait,
                MarginLeft = 1,
                MarginRight = 1,
                MarginTop = 5,
                MarginBottom = 5
            };
            renderer.RenderingOptions = option;
            renderer.RenderingOptions.PaperFit.UseFitToPageRendering();
            PdfDocument doc = await renderer.RenderHtmlAsPdfAsync(html);
            return doc.Stream.ToArray();
        }
    }
}
