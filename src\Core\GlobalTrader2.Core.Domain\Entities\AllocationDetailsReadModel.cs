﻿namespace GlobalTrader2.Core.Domain.Entities
{
    public class AllocationDetailsReadModel
    {
        public int AllocationId { get; set; }
        public int? StockNo { get; set; }
        public int QuantityAllocated { get; set; }
        public string? Part { get; set; }
        public bool? ROHS { get; set; }
        public double? LandedCost { get; set; }
        public int? QuantityInStock { get; set; }
        public int? PurchaseOrderNo { get; set; }
        public int? PurchaseOrderNumber { get; set; }
        public string? SupplierPart { get; set; }
        public int? PurchaseOrderLineNo { get; set; }
        public DateTime? ReturnDate { get; set; }
        public int? CompanyNo { get; set; }
        public string? CompanyName { get; set; }
        public double? Price { get; set; }
        public DateTime? ExpediteDate { get; set; }
        public int? CustomerRMANo { get; set; }
        public int? CustomerRMANumber { get; set; }
        public int? QuantityOnOrder { get; set; }
        public int? POSerialNo { get; set; }
        public int? InternalPurchaseOrderId { get; set; }
        public int? InternalPurchaseOrderNumber { get; set; }
        public int? IPOSupplier { get; set; }
        public string? IPOSupplierName { get; set; }
        public double? ClientLandedCost { get; set; }
    }
}
