﻿import { RequirementSourceManager } from "./requirement-source.js?v=#{BuildVersion}";
import { QuoteSourceManager } from "./quote-source.js?v=#{BuildVersion}";
import { SalesOrderLineSourceManager } from "./sales-order-line-source.js?v=#{BuildVersion}";
import { StockSourceManager } from "./stock-source.js?v=#{BuildVersion}";
import { ServiceSourceManager } from "./service-source.js?v=#{BuildVersion}";
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';
export class AddNewLineStep2Manager {
    constructor({ globalClientNo }) {
        this.selectedSource = null;
        this.requirementSourceManager = null;
        this.quoteSourceManager = null;
        this.salesOrderLineSourceManager = null;
        this.stockSourceManager = null;
        this.serviceSourceManager = null;
        this.globalClientNo = globalClientNo;
        this.eventHandlers = [];
        this.previousSelectedSource = null;
    }

    initialize() {
        this.registerEvent();
    }


    /**
     * Attach an event listener.
     * @param {string} event - The event name.
     * @param {Function} callback - The event handler function.
     */
    on(event, callback) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(callback);
    }

    /**
     * Trigger an event and call all attached handlers.
     * @param {string} event - The event name.
     * @param {any} data - Data to pass to the event handlers.
     */

    trigger(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(callback => callback(data));
        }
    }

    triggerContinueClicked(data) {
        this.trigger('continue.clicked', data)
    }

    loadSection(selectedSource) {
        this.previousSelectedSource = this.selectedSource;
        this.selectedSource = selectedSource;
        this.resetPreviousSelectedSource();
        switch (selectedSource) {
            case FromSourceTypeConstant.CUSREQ:
                if (this.requirementSourceManager == null) {
                    this.requirementSourceManager = new RequirementSourceManager({
                        onSelectedSourceItem: (data) => {
                            this.triggerContinueClicked(data);
                        }
                    });
                    this.requirementSourceManager.initialize();
                } else {
                    this.requirementSourceManager.clearTable();
                    this.requirementSourceManager.doSearch()
                }
                break;
            case FromSourceTypeConstant.QUOTE:
               
                if (this.quoteSourceManager == null) {
                    this.quoteSourceManager = new QuoteSourceManager({
                        onSelectedSourceItem: (data) => {
                            this.triggerContinueClicked(data);
                        }
                    });
                    this.quoteSourceManager.initialize();
                } else {
                    this.quoteSourceManager.clearTable();
                    this.quoteSourceManager.doSearch()
                }
                break;
            case FromSourceTypeConstant.SO:
                if (this.salesOrderLineSourceManager == null) {
                    this.salesOrderLineSourceManager = new SalesOrderLineSourceManager({
                        onSelectedSourceItem: (data) => {
                            this.triggerContinueClicked(data);
                        }
                    });
                    this.salesOrderLineSourceManager.initialize();
                } else {
                    this.salesOrderLineSourceManager.clearTable();
                    this.salesOrderLineSourceManager.doSearch()
                }
                break;
            case FromSourceTypeConstant.STOCK:
                if (this.stockSourceManager == null) {
                    this.stockSourceManager = new StockSourceManager({
                        onSelectedSourceItem: (data) => {
                            this.triggerContinueClicked(data);
                        }
                    });
                    this.stockSourceManager.initialize();
                } else {
                    this.stockSourceManager.clearTable();
                    this.stockSourceManager.doSearch()
                }
                break;
            case FromSourceTypeConstant.SERVICE:
                if (this.serviceSourceManager == null) {
                    this.serviceSourceManager = new ServiceSourceManager({
                        onSelectedSourceItem: (data) => {
                            this.triggerContinueClicked(data);
                        },
                        globalClientNo: this.globalClientNo
                    });
                    this.serviceSourceManager.initialize();
                } else {
                    this.serviceSourceManager.clearTable();
                    this.serviceSourceManager.doSearch()
                }
                break;
        }
    }

    resetPreviousSelectedSource() {
        if (this.previousSelectedSource != this.selectedSource) {
            switch (this.previousSelectedSource) {
                case FromSourceTypeConstant.CUSREQ:
                    this.requirementSourceManager.clearTable();
                    break;
                case FromSourceTypeConstant.QUOTE:
                    this.quoteSourceManager.clearTable();
                    break;
                case FromSourceTypeConstant.SO:
                    this.salesOrderLineSourceManager.clearTable();
                    break;
                case FromSourceTypeConstant.STOCK:
                    this.stockSourceManager.clearTable();
                    break;
                case FromSourceTypeConstant.SERVICE:
                    this.serviceSourceManager.clearTable();
                    break;
            }
        }
    }

    registerEvent() {
    }
}