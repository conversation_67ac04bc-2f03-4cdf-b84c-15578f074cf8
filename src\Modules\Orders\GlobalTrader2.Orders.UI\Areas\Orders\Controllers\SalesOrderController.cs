﻿using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.AddSaleOrder;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetCompanySalesInfo;
using GlobalTrader2.Dto.Company.CompanySalesInfo;
using GlobalTrader2.Dto.DataListNugget;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Orders.UI.ViewModel.Requirements.Request;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.AutoSearchSalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSalesOrderForTodoTask;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSoIdByNumber;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.SearchSalesOrders;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSoLineExportData;
using GlobalTrader2.Settings.UseCases.CompanySettings.Address.Queries;
using GlobalTrader2.SharedUI.Interfaces;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.UserAccount.UseCases.FilterState.Commands.SaveFilterStates;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;
[ApiController]
[Authorize]
[Route("api/orders/sales-orders")]
public class SalesOrderController : ApiBaseController
{
    private readonly IMediator _mediator;
    private readonly SessionManager _sessionManager;
    private readonly SecurityManager _securityManager;
    private readonly IStringLocalizer<SharedUI.Status> _statusLocalizer;
    private readonly IExportService _exportService;
    public SalesOrderController(IMediator mediator
        , SessionManager sessionManager
        , SecurityManager securityManager
        , IStringLocalizer<SharedUI.Status> statusLocalizer
        , IExportService exportService)
    {
        _mediator = mediator;
        _sessionManager = sessionManager;
        _securityManager = securityManager;
        _statusLocalizer = statusLocalizer;
        _exportService = exportService;
    }

    [HttpPost("auto-search")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AutoSearchQuotes([FromBody] AutoSearchSalesOrderRequest request)
    {
        var response = await _mediator.Send(new AutoSearchSalesOrderQuery
        {
            ClientNo = ClientId,
            Keyword = request.Keyword,
        });

        return new JsonResult(response);
    }
    [HttpGet("{id}/for-todo")]
    public async Task<IActionResult> GetSalesOrderForTodoTask([FromRoute] int id)
    {
        var response = await _mediator.Send(new GetSalesOrderForTodoTaskQuery
        {
            SalesOrderId = id
        });

        return new JsonResult(response);
    }
    [HttpGet("search-id")]
    public async Task<IActionResult> GetSaleOrderIdByNumber([FromQuery] int soNum)
    {
        return Ok(await _mediator.Send(new GetSaleOrderIdQuery()
        {
            ClientNo = ClientId,
            SONum = soNum,
            IsGlobalUser = IsGlobalLogin
        }));
    }

    [HttpGet("sales-info")]
    public async Task<IActionResult> GetCompanySalesInfoAsync([FromQuery] CompanySalesInfoRequest request)
    {
        request.ClientCurrencyId = ClientCurrencyId;
        request.ClientId = ClientId;
        return Ok(await _mediator.Send(new GetCompanySalesInfoQuery(request)));
    }

    [HttpGet("shipping-address/{addressId}")]
    public async Task<IActionResult> GetShippingAddressDetailAsync([FromRoute] int addressId)
    {
        var result = await _mediator.Send(new GetAddressQuery
        {
            AddressId = addressId,
        });
        return Ok(result);
    }

    [HttpPost]
    [ApiAuthorize(false, SecurityFunction.OrdersSection_View, SecurityFunction.Orders_SalesOrder_Add)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddSaleOrderAsync([FromBody] AddSaleOrderRequest request)
    {
        request.ClientNo = ClientId;
        request.OGELRequired = request.OGELRequired_Value == 1;
        request.LoginIdSession = UserId;
        request.SenderName = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}");
        request.SenderEmail = LoginEmail;
        request.QuoteLineIDs = request.QuoteLineIDsStr?
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(s => int.Parse(s))
            .ToList() ?? new();

        request.IsCopyAllQuoteLines = request.QuoteId > 0 && request.QuoteLineIDs.Count == 0;
        var response = await _mediator.Send(new AddSaleOrderCommand(request));
        return Ok(response);
    }

    [HttpPost("search-company")]
    [ApiAuthorize(false, SecurityFunction.Orders_SalesOrder_Add)]
    public async Task<IActionResult> SearchCompany([FromBody] SearchComSearchCompanyItemRequest request)
    {
        bool allowCheckedCompanyOnStop = await _securityManager.CheckFunctionPermissions(UserId, false, [SecurityFunction.Orders_SalesOrder_AllowCheckedCompanyOnStop]);

        var name = StringHelper.RemovePunctuationRetainingPercentSigns(request!.Name ?? string.Empty);

        var result = await _mediator.Send(new GetAllCompanyQuery
        {
            ClientId = ClientId,
            Name = name,
            POApproved = request.POApproved,
            SOApproved = request.SOApproved,
            SupplierStop = request.SupplierStop ?? false,
            AllowCheckedCompanyOnStop = allowCheckedCompanyOnStop,
            Index = request.Index,
            OrderBy = request.OrderBy ?? 1,
            SortDir = request.SortDir ?? 1,
            Size = request.Size,
            PoNoHi = null,
            PoNoLo = null,
        });

        var totalRecords = result?.Data?.FirstOrDefault()?.RowCnt;

        var response = new DatatableResponse<IEnumerable<CompanySearchItemDto>>()
        {
            Success = result!.Success,
            Data = result.Data,
            RecordsTotal = totalRecords ?? 0,
            RecordsFiltered = totalRecords ?? 0,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpPost("search-sales-orders")]
    [ApiAuthorize(isDataOtherClient: false, SecurityFunction.ContactSection_View)]
    public async Task<IActionResult> GetSalesOrdersAsync([FromBody] GetSalesOrdersFilterRequest request, CancellationToken cancellationToken)
    {
        try
        {
            request.PageSize = request.Length;
            request.PageIndex = request.Start / request.Length;
            request.LoginId = request.LoginId.HasValue ? request.LoginId : UserId;
            request.TeamId = LoginTeamID;
            request.DivisionId = LoginDivisionID;
            request.IsGSA = _sessionManager.IsGSA;
            request.CurrentClient = ClientId;
            request.SelectedLogin = UserId;
            request.IsGlobalLogin = _sessionManager.IsGlobalUser;
            var dataListStates = new DataListNuggetState<SalesOrdersFilter>();
            dataListStates.SortIndex = request.OrderBy - 1;
            dataListStates.SortDirection = request.SortDir;
            dataListStates.Page = request.PageIndex + 1;
            dataListStates.PageSize = request.PageSize;
            dataListStates.AddFilterStates(request.Filters);
            var salesOrdersFilterStates = dataListStates.ObjFilterStates;

            if (request.SaveStates)
            {
                await _mediator.Send(new SaveFilterStatesCommand
                {
                    DataListNuggetNo = (int)DataListNuggets.SalesOrders,
                    LoginNo = UserId,
                    StateText = dataListStates.ToString()
                }, cancellationToken);
            }
            var isMakeRowYellow = _sessionManager.IsGSA && !_sessionManager.IsGlobalUser && salesOrdersFilterStates?.ClientName != null;
            var result = await _mediator.Send(new SearchSalesOrdersQuery { Data = request, Filters = salesOrdersFilterStates }, cancellationToken);
            if (result.Data != null)
            {
                foreach (var item in result.Data)
                {
                    item.StatusDesc = item.Status.HasValue ? _statusLocalizer[((Core.Enums.SalesOrderStatus)item.Status).ToString()] : string.Empty;
                    item.IsMakeRowYellow = isMakeRowYellow;
                }
            }
            var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

            var response = new DatatableResponse<IEnumerable<SalesOrderLineDetailsDto>>()
            {
                Success = result.Success,
                Data = result.Data,
                RecordsTotal = totalItems,
                RecordsFiltered = totalItems,
                Draw = request.Draw
            };

            return Ok(response);
        }
        catch (TaskCanceledException)
        {
            HttpContext.Response.StatusCode = 499;
            return new ObjectResult(new BaseResponse<string>()
            {
                Data = "Request was canceled"
            });
        }
    }

    [HttpPost("download-excel")]
    [ApiAuthorize(isDataOtherClient: false, SecurityFunction.ContactSection_View)]
    public async Task<IActionResult> DownloadExcelAsync([FromBody] GetSalesOrdersFilterRequest request, CancellationToken cancellationToken)
    {
        request.LoginId = request.LoginId.HasValue ? request.LoginId : UserId;
        request.TeamId = LoginTeamID;
        request.DivisionId = LoginDivisionID;
        request.IsGSA = _sessionManager.IsGSA;
        request.CurrentClient = ClientId;
        request.SelectedLogin = UserId;
        request.IsGlobalLogin = _sessionManager.IsGlobalUser;
        var dataListStates = new DataListNuggetState<SalesOrdersFilter>();
        dataListStates.SortIndex = request.OrderBy - 1;
        dataListStates.SortDirection = request.SortDir;
        dataListStates.Page = request.PageIndex + 1;
        dataListStates.PageSize = request.PageSize;
        dataListStates.AddFilterStates(request.Filters);
        var salesOrdersFilterStates = dataListStates.ObjFilterStates;

        var result = await _mediator.Send(new GetSoLineExportDataQuery { Data = request, Filters = salesOrdersFilterStates }, cancellationToken);

        var items = result.Data;

        if (items == null) return new ObjectResult(new BaseResponse<string>());

        var fileName = $"report_u{UserId}r{(int)DataListExport.SalesOrder}.xlsx";
        var fileBytes = _exportService.ExportToExcelSalesOrder(items);

        Response.Headers.TryAdd("Content-Disposition", $"attachment; filename=\"{fileName}\"");
        return File(fileBytes, "text/csv");
    }
}
public record AutoSearchSalesOrderRequest(string Keyword);
