﻿using GlobalTrader2.Dto.SalesOrderLine;

namespace GlobalTrader2.Dto.SalesOrder
{
    public class SalesOrderForPrintDto
    {
        public string? LoginFullName { get; set; }
        public int SalesOrderId { get; set; }
        public int SalesOrderNumber { get; set; }
        public int ClientNo { get; set; }
        public int CompanyNo { get; set; }
        public int ContactNo { get; set; }
        public DateTime DateOrdered { get; set; }
        public int CurrencyNo { get; set; }
        public int Salesman { get; set; }
        public int TermsNo { get; set; }
        public int? ShipToAddressNo { get; set; }
        public int? ShipViaNo { get; set; }
        public string? Account { get; set; }
        public double Freight { get; set; }
        public string? CustomerPO { get; set; }
        public int DivisionNo { get; set; }
        public int TaxNo { get; set; }
        public double? ShippingCost { get; set; }
        public string? Notes { get; set; }
        public string? Instructions { get; set; }
        public bool Paid { get; set; }
        public int? StatusNo { get; set; }
        public bool Closed { get; set; }
        public int? SaleTypeNo { get; set; }
        public int? Salesman2 { get; set; }
        public double Salesman2Percent { get; set; }
        public int? AuthorisedBy { get; set; }
        public DateTime? DateAuthorised { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime DLUP { get; set; }
        public DateTime? CurrencyDate { get; set; }
        public int? IncotermNo { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreateDate { get; set; }
        public bool? IsPDFAvailable { get; set; }
        public bool? IsSORPDFAvailable { get; set; }
        public bool? AS9120 { get; set; }
        public int? DivisionNo2 { get; set; }
        public int? UKAuthorisedBy { get; set; }
        public DateTime? UKAuthorisedDate { get; set; }
        public bool? IsConsolidated { get; set; }
        public int? SentOrdertoCust { get; set; }
        public DateTime? SentOrderDate { get; set; }
        public int? SupportTeamMemberNo { get; set; }
        public int? RefIdHK { get; set; }
        public int? DivisionHeaderNo { get; set; }
        public string? HeaderImageName { get; set; }
        public int? SystemDocumentFooterHistoryNo { get; set; }
        public int? SysDocAS9120HistoryNo { get; set; }
        public int? SysDocHazardousHistoryNo { get; set; }
        public int? SysDocCOOHistoryNo { get; set; }
        public int? SysDocProFormaHistoryNo { get; set; }
        public bool? IsExcelDocAvailable { get; set; }
        public string? Comment { get; set; }
        public bool? IsPowerAppApproved { get; set; }
        public string? OGEL_EndUserNotes { get; set; }
        public int? OGEL_EndDestinationCountry { get; set; }
        public int? OGEL_MilitaryUse { get; set; }
        public bool? OGEL_Required { get; set; }
        public bool? AS6081 { get; set; }
        public bool? IsAllowReadyTOShip { get; set; }
        public string CompanyName { get; set; } = null!;
        public string? CustomerCode { get; set; }
        public string? CompanyTelephone { get; set; }
        public string? CompanyFax { get; set; }
        public bool? ShippingCharge { get; set; }
        public string? VATNo { get; set; }
        public bool IsRebateAccount { get; set; }
        public string? ContactName { get; set; }
        public string? ContactEmail { get; set; }
        public string CurrencyCode { get; set; } = null!;
        public string CurrencyDescription { get; set; } = null!;
        public string? CurrencyNotes { get; set; }
        public string? SalesmanName { get; set; }
        public string TermsName { get; set; } = null!;
        public string? ShipViaName { get; set; }
        public string TaxName { get; set; } = null!;
        public double? TaxRate { get; set; }
        public double? LineSubTotal { get; set; }
        public double? TotalTax { get; set; }
        public double? TotalValue { get; set; }
        public string? IncotermName { get; set; }
        public string? Salesman2Name { get; set; }
        public string? CompanyRegNo { get; set; }
        public bool? IsApplyBankFee { get; set; }
        public double? InvoiceBankFee { get; set; }
        public string? CompanyType { get; set; }
        public bool IsTraceability { get; set; }
        public bool? IsAgency { get; set; }
        public string? UKAuthoriserName { get; set; }
        public bool? CompanyOnStop { get; set; }
        public int? AppliedDivisionNoForWHS { get; set; }
        public string? DivisionHeaderName { get; set; }
        public string? FooterText { get; set; }
        public string? SysDocAS9120HistoryText { get; set; }
        public string? SysDocHazardousHistoryText { get; set; }
        public string? SysDocCOOHistoryText { get; set; }
        public string? SysDocProFormaHistoryText { get; set; }
        public string? dBillLabelTypeName { get; set; }
        public string? dShipLabelTypeName { get; set; }
        public decimal CurrencyRate { get; set; }
        public string? ShipToAdressName { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string AllocatedPath { get; set; } = string.Empty;
        public string ShippedPath { get; set; } = string.Empty;
        public string IhsPartstatusPath { get; set; } = string.Empty;
        public string  HazardousPath { get; set; } = string.Empty;
        public string BaseDirectoryPath { get; set; } = string.Empty;
        public required List<SalesOrderLineForSOReportDto> SalesOrderLineList { get; set; }


    }
    public class SalesOrderLineForSOReportDto()
    {
        public int SalesOrderLineId { get; set; }
        public int SalesOrderNo { get; set; }
        public string Taxable { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public double Price { get; set; }
        public byte? ROHS { get; set; }
        public string? CustomerPart { get; set; }
        public string Part { get; set; } = string.Empty;
        public int? ManufacturerNo { get; set; }
        public string? ManufacturerCode { get; set; }
        public string? DateCode { get; set; }
        public int? ProductNo { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public int? PackageNo { get; set; }
        public string? PackageName { get; set; }
        public int QuantityShipped { get; set; }
        public int QuantityAllocated { get; set; }
        public int BackOrderQuantity { get; set; }
        public int? ServiceNo { get; set; }
        public bool Closed { get; set; }
        public bool Posted { get; set; }
        public bool Inactive { get; set; }
        public bool ServiceShipped { get; set; }
        public double? ServiceCost { get; set; }
        public double? ServicePrice { get; set; }
        public DateTime? DatePromised { get; set; }
        public string? Notes { get; set; }
        public byte? ProductSource { get; set; }
        public bool? ShipASAP { get; set; }
        public int? SOSerialNo { get; set; }
        public bool? IsIPO { get; set; }
        public bool? IsChecked { get; set; }
        public int? SourcingResultNo { get; set; }
        public int? IsIPOHeaderCreated { get; set; }
        public int? InternalPurchaseOrderNumber { get; set; }
        public int? ClonedId { get; set; }
        public string? DutyCode { get; set; }
        public bool? PrintHazardous { get; set; }
        public string? MSLLevel { get; set; }
        public string? ContractNo { get; set; }
        public bool? IsConfirmed { get; set; }
        public DateTime? DateConfirmed { get; set; }
        public DateTime? DateChanged { get; set; }
        public int? PromiseReasonNo { get; set; }
        public string? PromiseReason { get; set; }
        public int? WarehouseNo { get; set; }
        public string? WarehouseName { get; set; }
        public string? PostDisableReason { get; set; }
        public bool? IsProdHazardous { get; set; }
        public bool? IsOrderViaIPOonly { get; set; }
        public string? ECCNCode { get; set; }
        public int? ECCNCodeNo { get; set; }
        public bool? IsECCNWarning { get; set; }
        public bool? IsEUUPDFAvailable { get; set; }
        public DateTime? EUUPDFUploadDate { get; set; }
        public string? EUUUPDFploadName { get; set; }
        public bool? AS6081 { get; set; }
        public int? RestrictedMfrNo { get; set; }
        public bool? RestrictedMfrInactive { get; set; }
        public bool IsLineTaxable { get; set; }

        public bool IsAllocated { get; set; }
        public bool IsService { get; set; }

        public bool IsShipped { get; set; }
        public decimal SoPriceInBase { get; set; }
        public decimal LineTotalSell { get; set; }

        public required List<SalesOrderLineReportPoStockDto> SalesOrderLineReportPoStockList { get; set; } = new List<SalesOrderLineReportPoStockDto>();
        public required List<SalesOrderLineReportPoDto> SalesOrderLineReportPoList { get; set; } = new List<SalesOrderLineReportPoDto>();
        public required List<SalesOrderLineReportManualStockDto> SalesOrderLineReportManualStockList { get; set; } = new List<SalesOrderLineReportManualStockDto>();
        public required List<SalesOrderLineReportShippedDto> SalesOrderLineReportShippedList { get; set; } = new List<SalesOrderLineReportShippedDto>();
    }
}
