using GlobalTrader2.Dto.PurchaseRequisition;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseRequisition.Queries;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.UserAccount.UseCases.RecentlyViewed.Commands.Create;
using Microsoft.AspNetCore.Http;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseRequisition.Details
{
    [SectionAuthorize(SecurityFunction.OrdersSection_View)]
    public class IndexModel : BasePageModel
    {
        [FromQuery(Name = "prq")]
        public int PurchaseRequisitionQuery { get; set; }
        public SalesOrderLineForPurchaseRequisitionPageDto PurchaseRequisitionHeaderInfo { get; set; } = new();
        private readonly IMediator _mediator;
        public readonly SessionManager _sessionManager;
        public IndexModel(IMediator mediator, SessionManager sessionManager)
        {
            _mediator = mediator;
            _sessionManager = sessionManager;
            PageType = SitePage.Orders_PurchaseRequisitionDetail;
            SiteSection = SiteSection.Orders;
        }
        public async Task<IActionResult> OnGet()
        {
            var result = await _mediator.Send(new GetSalesOrderLineForPurReqPageQuery()
            {
                SalesOrderLineId = PurchaseRequisitionQuery,
            });

            if (!result.Success || result.Data == null) return Redirect("/NotFound");
            if (_sessionManager.ClientID != result.Data.ClientNo)
            {
                return Redirect("/NotFound");
            }
            PurchaseRequisitionHeaderInfo = result.Data;
            AddBreadCrumbs();

            await UpdateRecentlyView();

            return Page();

        }
        private async Task UpdateRecentlyView()
        {
            var lastBreadcrumb = BreadCrumb[BreadCrumb.Count - 1];
            var command = new CreateRecentlyViewedCommand
            {
                LoginNo = HttpContext.Session.GetInt32(SessionKey.LoginID),
                PageTitle = lastBreadcrumb.PageTitle,
                PageUrl = $"{lastBreadcrumb.CtaUri}{HttpContext.Request.QueryString.Value}"
            };
            await _mediator.Send(command);
        }
        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.PurchaseRequisitions);
            BreadCrumb.Add(Navigations.PurchaseRequisitionDetail($"{PurchaseRequisitionHeaderInfo.SalesOrderNumber}/{PurchaseRequisitionHeaderInfo.SalesOrderLineId}"));
            BreadCrumbMenu.Add(BreadCrumbHelper.Order_MenuNew(_securityManager!));
        }

    }
}
