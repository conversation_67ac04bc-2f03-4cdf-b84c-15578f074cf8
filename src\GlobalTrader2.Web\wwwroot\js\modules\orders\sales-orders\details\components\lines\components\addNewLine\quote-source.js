﻿import { SourceFilterComponent } from './source-filter-component.js?v=#{BuildVersion}#';
import { SourceItemModel } from '../../models/source-item.model.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';
import { FieldType } from '../../../../../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { NumberType } from "../../../../../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#";
import { TextFilterHelper } from "../../../../../../../../helper/text-filter-helper.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#";
export class QuoteSourceManager {
    constructor({ onSelectedSourceItem = (data) => { } }) {
        this.tableFilter = null;
        this.quoteSourceTable = null;
        this.onSelectedSourceItem = onSelectedSourceItem;
        this.quoteSourceUrl = "/api/orders/quotes/quote-line/item-search";
        this.pageSize = $(`#add-lines-dialog`).data('default-page-size') || 10;
        this.filterInputs = [
            {
                fieldType: FieldType.NUMBER,
                label: 'Quote No',
                name: 'QuoteNo',
                id: 'QuoteNo',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true,
                },
                extraPros: {
                    numberType: NumberType.INT
                },
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Part No',
                name: 'PartNo',
                id: 'PartNo',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Company',
                name: 'Company',
                id: 'Company',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: 'Include Closed?',
                name: 'IncludeClosed',
                id: 'IncludeClosed',
                value: '0',
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Quoted From',
                name: 'DateQuotedFrom',
                id: 'DateQuotedFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-2',
                pairWith: "DateQuotedTo"
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Quoted To',
                name: 'DateQuotedTo',
                id: 'DateQuotedTo',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
        ]
    }

    async initialize() {
        $("#quotes-source").show();
        await this.initTableFilter();
        this.setDefaultFilter();
        this.initDataTable();
    }

    setDefaultFilter() {
        const companyInput = this.tableFilter.getInputElementByName("Company");
        companyInput.setRequiredCheckbox(true);
        companyInput.setValue(TextFilterHelper.getFilterValue(companyInput.textFilterComponent.currentFilterIndex, SODetailGeneralInfo.companyName));
        companyInput.syncInputState();
        companyInput.triggerControlChanged();

        const dateQuotedFromInput = this.tableFilter.getInputElementByName("DateQuotedFrom");
        dateQuotedFromInput.setRequiredCheckbox(true);
        dateQuotedFromInput.setValue(GlobalTrader.DatetimeHelper.oneWeekAgo());
        dateQuotedFromInput.syncInputState();
        dateQuotedFromInput.triggerControlChanged();
    }

    async initTableFilter() {
        this.tableFilter = new SourceFilterComponent('#quotes-source-filter-section-wrapper', 'Search for and select the item you would like to use as the source for the new Line and press Continue', {
            inputConfigs: this.filterInputs,
            wrapperClass: 'bg-none m-0 p-0'
        });

        await this.tableFilter.init();
        this.tableFilter.on('applied.mtf', () => {
            this.quoteSourceTable.ajax.url(this.quoteSourceUrl);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.quoteSourceTable, true);
        })

        this.tableFilter.on('cancel.mtf', () => {
            if (window.currentXhr) {
                window.currentXhr.abort();
                if ($('#quotesSourceTbl_processing').is(':visible')) {
                    $('#quotesSourceTbl_processing').hide();
                }
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        })
    }

    initDataTable() {
        this.quoteSourceTable = new DataTable('#quotesSourceTbl', {
            scrollCollapse: true,
            paging: true,
            dataSrc: 'data',
            serverSide: true,
            lengthMenu: [5, 10, 25, 50],
            pageLength: this.pageSize,
            ajax: {
                url: this.tableFilter.hasAnyActiveFilter() ? this.quoteSourceUrl : "",
                type: 'POST',
                contentType: 'application/json',
                beforeSend: (xhr) => {
                    window.currentXhr = xhr;
                },
                data: (data) => {
                    const filterValues = this.tableFilter.getAllValue();
                    return JSON.stringify({
                        quoteNoLo: filterValues.QuoteNo.isOn ? filterValues.QuoteNo.low : null,
                        quoteNoHi: filterValues.QuoteNo.isOn ? filterValues.QuoteNo.hi : null,
                        includeClosed: filterValues.IncludeClosed.isOn ? filterValues.IncludeClosed.value : false,
                        partSearch: filterValues.PartNo.isOn ? filterValues.PartNo.value : null,
                        companySearch: filterValues.Company.isOn ? filterValues.Company.value : null,
                        dateQuotedFrom: filterValues.DateQuotedFrom.isOn ? this.formatDateFilter(filterValues.DateQuotedFrom.value) : null,
                        dateQuotedTo: filterValues.DateQuotedTo.isOn ? this.formatDateFilter(filterValues.DateQuotedTo.value) : null,
                        draw: data.draw,
                        index: data.start,
                        size: data.length,
                        sortDir: data.order[0]?.column ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : 1,
                        orderBy: data.order[0]?.column ? data.order[0].column : 1,
                    });
                },
            },
            info: true,
            responsive: true,
            select: {
                style: 'single'
            },
            ordering: true,
            searching: false,
            processing: true,
            columnDefs: [
                { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
            ],
            language: {
                emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                infoFiltered: "",
                lengthMenu: "_MENU_ per page",
                loadingRecords: "",
            },
            dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                '<"dt-layout-cell dt-layout-end" p >><"clear">',
            rowId: "quoteLineId",
            order: [[0, "desc"]],
            columns: [
                {
                    data: "quoteNumber",
                    title: "Quote",
                    type: 'string',
                },
                {
                    data: "companyName",
                    title: "Company",
                    type: 'string',
                },
                {
                    data: null,
                    title: "Part No",
                    render: (row) => `${ROHSHelper.writePartNo(row.part, row.rohs)}`
                },
                {
                    data: "dateQuotedText",
                    title: "Quoted",
                    type: 'string',
                },
                {
                    data: "quantity",
                    title: "Quantity",
                    type: 'string',
                },
                {
                    data: "formatedPrice",
                    title: "Unit Price",
                    type: 'string',
                },
            ]
        }).on('draw.dt', () => {
            this.quoteSourceTable.columns.adjust();
        }).on('processing.dt', (e, settings, processing) => {
            this.removeNeutralSortingIcon(this.quoteSourceTable);
            if (processing) {
                // Table is processing (e.g., AJAX call happening)
                this.tableFilter.toggleApplyCancelButtons(false);
            } else {
                // Done processing
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        });

        this.quoteSourceTable.on('select', async (e, dt, type, indexes) => {
            if (type === 'row') {
                const selectedRowId = dt.row(indexes).id();
                this.onSelectedSourceItem(new SourceItemModel(FromSourceTypeConstant.QUOTE, selectedRowId));
            };
        })
    }

    removeNeutralSortingIcon(datatable) {
        // Remove neutral sorting icon
        const tableId = datatable.table().node().id;
        $(`#${tableId} thead th`)
            .removeClass('dt-orderable-asc dt-orderable-desc')
            .addClass('position-relative');

        $(`#${tableId} thead th:not(.dt-orderable-none)`)
            .attr('role', 'button');

        $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
    }

    clearTable() {
        $("#quotes-source").hide();
        if ($('#quotesSourceTbl_processing').is(':visible')) {
            $('#quotesSourceTbl_processing').hide();
        }
        if (this.quoteSourceTable != null) {
            this.quoteSourceTable.rows('.selected').deselect();
            this.quoteSourceTable.ajax.url('').load();
            this.quoteSourceTable.ajax.url(this.quoteSourceUrl);
        }
    }

    doSearch() {
        $("#quotes-source").show();
        if (this.tableFilter.hasAnyActiveFilter()) {
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.quoteSourceTable, true);
        }
    }

    formatDateFilter(dateString) {
        const [day, month, year] = dateString.split("/");
        return `${year}-${month}-${day}T00:00:00.000Z`
    }
}