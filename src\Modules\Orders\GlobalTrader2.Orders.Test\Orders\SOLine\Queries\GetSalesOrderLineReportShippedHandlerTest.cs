﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPO;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportShipped;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Queries
{
    public class GetSalesOrderLineReportShippedHandlerTest
    {
        private readonly Mock<IBaseRepository<SalesOrderLineReportShippedReadModel>> _salesOrderLinePoRepository;
        private readonly Mock<IMapper> _mapper;
        private readonly GetSalesOrderLineReportShippedHandler _handler;
        private readonly IFixture _fixture = new Fixture();
        public GetSalesOrderLineReportShippedHandlerTest()
        {
            _salesOrderLinePoRepository = new Mock<IBaseRepository<SalesOrderLineReportShippedReadModel>>();
            _mapper = new Mock<IMapper>();
            _handler = new GetSalesOrderLineReportShippedHandler(_salesOrderLinePoRepository.Object, _mapper.Object);
        }
        [Fact]
        public async Task GetSalesOrderLineReportShippedHandlerTest_ReturnValueTest()
        {
            _salesOrderLinePoRepository.Setup(z => z.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>())).ReturnsUsingFixture(_fixture);
            _mapper.Setup(z => z.Map<IReadOnlyList<SalesOrderLineReportShippedDto>>(It.IsAny<IReadOnlyList<SalesOrderLineReportShippedReadModel>>()))
                .ReturnsUsingFixture(_fixture);
            var request = _fixture.Create<GetSalesOrderLineReportShippedQuery>();
            var result = await _handler.Handle(request, CancellationToken.None);
            Assert.NotNull(result);
            Assert.True(result.Success);
        }
    }
}
