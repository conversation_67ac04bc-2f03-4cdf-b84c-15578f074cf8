﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Aggregator.UseCases.Helper
{
    public static class ConvertImageToBase64Helper
    {
        private const string base64Format = "data:image/jpg;base64, {0}";
        public static string ConvertImageToBase64(string baseDirectory, string imagePath)
        {
            if (string.IsNullOrEmpty(baseDirectory) || string.IsNullOrEmpty(imagePath))
                return string.Empty;

            string path = Path.Combine(baseDirectory, imagePath);
            var imageBytes = File.ReadAllBytes(path);
            return string.Format(base64Format, Convert.ToBase64String(imageBytes));
        }
    }
}
