using GlobalTrader2.Dto.Orders.PurchaseOrder;
using GlobalTrader2.Dto.PurchaseOrder;

namespace GlobalTrader2.Orders.UserCases.Commons.Mappings;

using GlobalTrader2.Core.Helpers;

public class PurchaseOrderMapper : Profile
{
    public PurchaseOrderMapper()
    {
        CreateMap<PurchaseOrder, PurchaseOrderByNumberDto>().ReverseMap();
        CreateMap<PurchaseOrderDetailsReadModel, PurchaseOrderMainInfoDto>()
            .ForMember(x => x.SupplierName, d => d.MapFrom(x => x.CompanyName))
            .ForMember(x => x.SupplierNo, d => d.MapFrom(x => x.CompanyNo))
            .ForMember(x => x.DateOrderedRaw, d => d.MapFrom(x => x.DateOrdered))
            .ForMember(x => x.DateOrdered, d => d.MapFrom(x => Functions.FormatDate(x.DateOrdered)))
            .ForMember(x => x.ExpediteDate, d => d.MapFrom(x => Functions.FormatDate(x.ExpediteDate)))
            .ForMember(x => x.ExpediteNotes, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.ExpediteNotes ?? string.Empty)))
            .ForMember(x => x.ShippingAccountNo, d => d.MapFrom(x => x.Account))
            .ForMember(x => x.Notes, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.Notes ?? string.Empty)))
            .ForMember(x => x.Instructions, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.Instructions ?? string.Empty)))
            .ForMember(x => x.CountryWarningMessage, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.WarningText ?? string.Empty)))
            .ForMember(x => x.WebsiteAddress, d => d.MapFrom(x => x.Website))
            .ForMember(x => x.SupplierRMAIds, d => d.Ignore())
            .ForMember(x => x.DebitIds, d => d.Ignore())
            .ForMember(x => x.SupplierRMANumbers, d => d.Ignore())
            .ForMember(x => x.EPRIds, d => d.Ignore())
            .ForMember(x => x.POLineEPRIds, d => d.Ignore())
            .ForMember(x => x.DebitNumbers, d => d.Ignore());
        CreateMap<PoLineWarning, PoWarningDto>();
        CreateMap<PurchaseOrderForPageReadModel, PurchaseOrderForPageDto>();
    }

}