﻿using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetSOReportPdf;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Interfaces;

namespace GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.UploadSalesOrderReportPdf
{
    public class UploadSalesOrderReportPdfHandler : IRequestHandler<UploadSalesOrderReportPdfCommand, BaseResponse<string>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly string directory = "SOREPORT";
        private readonly string contentType = "application/pdf";
        private readonly ISender _sender;
        private readonly IBaseRepository<object> _objectRepository;
        private const string FileType = "SORPDFNEW";
        private const string Caption = "Approval_SOR";
        public UploadSalesOrderReportPdfHandler(IBlobStorageService blobStorageService, ISender sender, IBaseRepository<object> objectRepository)
        {
            _blobStorageService = blobStorageService;
            _sender = sender;
            _objectRepository = objectRepository;
        }
        public async Task<BaseResponse<string>> Handle(UploadSalesOrderReportPdfCommand request, CancellationToken cancellationToken)
        {
            var pdfFileQuery = await _sender.Send(new GetSOReportPdfQuery() { SalesOrderId = request.SalesOrderId, ClientId = request.ClientId, LoginFullName = request.LoginFullName, BaseDirectoryPath = request.BaseDirectory }, cancellationToken);
            if (pdfFileQuery != null && pdfFileQuery.Success && pdfFileQuery.Data != null)
            {
                var pdfFile = pdfFileQuery.Data;
                if (pdfFile.File != null && pdfFile.File.Length > 0)
                {
                    string blobName = $"{directory}/{pdfFile.FileName}";

                    await UploadFileToBlobStorage(blobName, pdfFile.File);
                    var output = new SqlParameter("@NewId", SqlDbType.Int) { Direction = ParameterDirection.Output };
                    //create pdf here
                    var insertPDFParams = new List<SqlParameter>()
                    {
                        new SqlParameter("@SalesOrderId",SqlDbType.Int) {Value = request.SalesOrderId },
                        new SqlParameter("@Caption",SqlDbType.NVarChar) {Value = Caption},
                        new SqlParameter("@FileName",SqlDbType.NVarChar) {Value = pdfFile.FileName},
                        new SqlParameter("@UpdatedBy",SqlDbType.Int) {Value = request.LoginId },
                        new SqlParameter("@FileType",SqlDbType.NVarChar) {Value = FileType },
                        output
                    };
                    await _objectRepository.ExecuteSqlRawAsync($"{StoredProcedures.Insert_SalesOrderPDF} " +
                        $"@SalesOrderId, @Caption, @FileName, @UpdatedBy,@NewId output, @FileType"
                        , insertPDFParams.ToArray());
                    return new BaseResponse<string>()
                    {
                        Success = (int)output.Value > 0,
                        Data = pdfFile.FileName
                    };
                }

            }
            return new BaseResponse<string>()
            {
                Success = false,
                Data = null,

            };
        }
        private async Task UploadFileToBlobStorage(string blobName, Stream file)
        {
            await _blobStorageService.UploadBlobAsync(BlobStorage.DocumentHeadersContainerName, blobName, contentType, file);

        }
    }
}
