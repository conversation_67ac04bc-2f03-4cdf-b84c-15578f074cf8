﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportManualStock
{
    public class GetSalesOrderLineReportManualStockQuery : IRequest<BaseResponse<IReadOnlyList<SalesOrderLineReportManualStockDto>>>
    {
        public int SalesOrderLineId { get; set; }
        public GetSalesOrderLineReportManualStockQuery(int SalesOrderLineId)
        {
            this.SalesOrderLineId = SalesOrderLineId;
        }
    }
}
