﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Core.Constants;

namespace GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.UpdateAuthorise
{
    public class UpdateAuthoriseCommand : IRequest<BaseResponse<bool>>
    {   public required int ClientId { get; set; }
        public required int LoginId { get; set; }
        public required int SalesOrderId  { get; set; }
        public string? Comment { get; set; }
        public required bool IsAuthorise { get; set; }
        public required bool IsNotify { get; set; }
        public required bool IsAllowReadyToShip { get; set; }
        public required string LoginEmail { get; set; }
        public required string CreateIPOSubject { get; set; } = string.Empty;
        public required string LoginName { get; set; } = string.Empty;
        public string? SOAuthoriseSubject { get; set; } = string.Empty;
        public string HostUrl { get; set; } = string.Empty;
        public required string PurchaseOrderUrl { get; set; }
        public required string InternalPurchaseOrderUrl { get; set; }
        public required string BaseDirectory { get; set; }
       

    }
}
