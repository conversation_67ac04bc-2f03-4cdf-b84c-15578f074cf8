﻿import { SourceFilterComponent } from './source-filter-component.js?v=#{BuildVersion}#';
import { SourceItemModel } from '../../models/source-item.model.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';
import { FieldType } from '../../../../../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { NumberType } from "../../../../../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#";
import { TextFilterHelper } from "../../../../../../../../helper/text-filter-helper.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#";
export class RequirementSourceManager {
    constructor({ onSelectedSourceItem = (data) => { } }) {
        this.tableFilter = null;
        this.requirementsSourceTable = null;
        this.sourcingResultTable = null;
        this.onSelectedSourceItem = onSelectedSourceItem;
        this.requirementsSourceUrl = "/api/orders/sales-order/so-lines/search-requirements";
        this.pageSize = $(`#add-lines-dialog`).data('default-page-size') || 10;
        this.filterInputs = [
            {
                fieldType: FieldType.NUMBER,
                label: 'Requirement No',
                name: 'RequirementNo',
                id: 'RequirementNo',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true,
                },
                extraPros: {
                    numberType: NumberType.INT
                },
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Part No',
                name: 'PartNo',
                id: 'PartNo',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Company',
                name: 'Company',
                id: 'Company',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: 'Include Closed?',
                name: 'IncludeClosed',
                id: 'IncludeClosed',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Received From',
                name: 'DateReceivedFrom',
                id: 'DateReceivedFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-2',
                pairWith: "DateReceivedTo"
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Received To',
                name: 'DateReceivedTo',
                id: 'DateReceivedTo',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
        ]
    }

    async initialize() {
        $("#requirements-source").show();
        await this.initTableFilter();
        this.setDefaultFilter();
        this.initDataTable();
    }

    setDefaultFilter() {
        const companyInput = this.tableFilter.getInputElementByName("Company");
        companyInput.setRequiredCheckbox(true);
        companyInput.setValue(TextFilterHelper.getFilterValue(companyInput.textFilterComponent.currentFilterIndex, SODetailGeneralInfo.companyName));
        companyInput.syncInputState();
        companyInput.triggerControlChanged();

        const dateReceivedFromInput = this.tableFilter.getInputElementByName("DateReceivedFrom");
        dateReceivedFromInput.setRequiredCheckbox(true);
        dateReceivedFromInput.setValue(GlobalTrader.DatetimeHelper.oneWeekAgo());
        dateReceivedFromInput.syncInputState();
        dateReceivedFromInput.triggerControlChanged();
    }

    async initTableFilter() {
        this.tableFilter = new SourceFilterComponent('#requirements-source-filter-section-wrapper', 'Search for and select the item you would like to use as the source for the new Line and press Continue', {
            inputConfigs: this.filterInputs,
            wrapperClass: 'bg-none m-0 p-0'
        });

        await this.tableFilter.init();

        this.tableFilter.on('applied.mtf', () => {
            this.requirementsSourceTable.ajax.url(this.requirementsSourceUrl);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.requirementsSourceTable, true);
        })

        this.tableFilter.on('cancel.mtf', () => {
            if (window.currentXhr) {
                window.currentXhr.abort();
                if ($('#requirementsSourceTbl_processing').is(':visible')) {
                    $('#requirementsSourceTbl_processing').hide();
                }
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        })
    }
    formatDateFilter(dateString) {
        const [day, month, year] = dateString.split("/");
        return `${year}-${month}-${day}T00:00:00.000Z`
    }

    initDataTable() {
        this.requirementsSourceTable = new DataTable('#requirementsSourceTbl', {
            scrollCollapse: true,
            paging: true,
            dataSrc: 'data',
            serverSide: true,
            lengthMenu: [5, 10, 25, 50],
            pageLength: this.pageSize,
            ajax: {
                url: this.tableFilter.hasAnyActiveFilter() ? this.requirementsSourceUrl : "",
                type: 'POST',
                contentType: 'application/json',
                beforeSend: (xhr) => {
                    window.currentXhr = xhr;
                },
                data: (data) => {
                    const filterValues = this.tableFilter.getAllValue()

                    return JSON.stringify({
                        customerRequirementNoLo: filterValues.RequirementNo.isOn ? filterValues.RequirementNo.low : null,
                        customerRequirementNoHi: filterValues.RequirementNo.isOn ? filterValues.RequirementNo.hi : null,
                        includeClosed: filterValues.IncludeClosed.isOn ? filterValues.includeClosed.value : null,
                        partSearch: filterValues.PartNo.isOn ? filterValues.PartNo.value : null,
                        companySearch: filterValues.Company.isOn ? filterValues.Company.value : null,
                        receivedDateFrom: filterValues.DateReceivedFrom.isOn ? this.formatDateFilter(filterValues.DateReceivedFrom.value) : null,
                        receivedDateTo: filterValues.DateReceivedTo.isOn ? this.formatDateFilter(filterValues.DateReceivedTo.value) : null,
                        draw: data.draw,
                        index: data.start,
                        size: data.length,
                        sortDir: data.order[0]?.column ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : 1,
                        orderBy: data.order[0]?.column ? data.order[0].column : 1,
                    });
                },
            },
            info: true,
            responsive: true,
            select: {
                style: 'single'
            },
            ordering: true,
            searching: false,
            processing: true,
            columnDefs: [
                { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
            ],
            language: {
                emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                infoFiltered: "",
                lengthMenu: "_MENU_ per page",
                loadingRecords: "",
            },
            dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                '<"dt-layout-cell dt-layout-end" p >><"clear">',
            rowId: "customerRequirementId",
            order: [[0, "desc"]],
            columns: [
                {
                    data: "customerRequirementNumber",
                    title: "Req",
                    type: 'string',
                },
                {
                    data: "companyName",
                    title: "Company",
                    type: 'string',
                },
                {
                    data: null,
                    title: "Part No",
                    render: (row) => `${ROHSHelper.writePartNo(row.part, row.rohs)}`
                },
                {
                    data: "receivedDateText",
                    title: "Date",
                    type: 'string',
                },
                {
                    data: "quantity",
                    title: "Quantity",
                    type: 'string',
                },
                {
                    data: "formatedPrice",
                    title: "Unit Price",
                    type: 'string',
                },
            ]
        }).on('draw.dt', () => {
            this.requirementsSourceTable.columns.adjust();
        }).on('processing.dt', (e, settings, processing) => {
            this.removeNeutralSortingIcon(this.requirementsSourceTable);
            if (processing) {
                // Table is processing (e.g., AJAX call happening)
                this.tableFilter.toggleApplyCancelButtons(false);
            } else {
                // Done processing
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        });

        this.requirementsSourceTable.on('select', async (e, dt, type, indexes) => {
            if (type === 'row') {
                const selectedRowId = dt.row(indexes).id();
                this.getSourcingResult(selectedRowId);
            };
        })
    }

    clearTable() {
        $("#requirements-source").hide();
        $("#sourcingResultSource-wrapper").hide();
        if ($('#requirementsSourceTbl_processing').is(':visible')) {
            $('#requirementsSourceTbl_processing').hide();
        }

        if ($('#sourcingResultTbl_processing').is(':visible')) {
            $('#sourcingResultTbl_processing').hide();
        }

        if (this.requirementsSourceTable != null) {
            this.requirementsSourceTable.rows('.selected').deselect();
            this.requirementsSourceTable.ajax.url('').load();
            this.requirementsSourceTable.ajax.url(this.requirementsSourceUrl);
            
        }
        if (this.sourcingResultTable != null) {
            this.sourcingResultTable.rows('.selected').deselect();
            this.sourcingResultTable.clear().draw();
        }
    }

    doSearch() {
        $("#requirements-source").show();
        if (this.tableFilter.hasAnyActiveFilter()) {
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.requirementsSourceTable, true);
        }
    }

    getSourcingResult(selectedCustomerRequirementId) {
         if (this.sourcingResultTable == null) {
             this.sourcingResultTable = new DataTable('#sourcingResultTbl', {
                 scrollCollapse: true,
                 paging: false,
                 dataSrc: 'data',
                 serverSide: false,
                 ajax: {
                     url: `/api/orders/customer-requirements/${selectedCustomerRequirementId}/sourcing-results`,
                     type: 'GET',
                     contentType: 'application/json',
                     beforeSend: (xhr) => {
                         window.currentXhr = xhr;
                         $("#sourcingResultSource-wrapper").show();
                     },
                     dataSrc: (json) => {
                         if (json.data && json.data.length === 0) {
                             $("#sourcingResultSource-wrapper").hide();
                             const selectedRowId = this.requirementsSourceTable.row({ selected: true }).id();
                             this.onSelectedSourceItem(new SourceItemModel(FromSourceTypeConstant.CUSREQ, selectedRowId));
                         }
                         return json.data;
                     }

                 },
                 info: true,
                 responsive: true,
                 select: {
                     style: 'single'
                 },
                 ordering: true,
                 searching: false,
                 processing: true,
                 columnDefs: [
                     { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
                 ],
                 language: {
                     emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                     zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                     infoFiltered: "",
                     lengthMenu: "_MENU_ per page",
                     loadingRecords: "",
                 },
                 dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                     '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                     '<"dt-layout-cell dt-layout-end" p >><"clear">',
                 rowId: "sourcingResultId",
                 order: [[0, "desc"]],
                 columns: [
                     {
                         data: "supplierName",
                         title: "Supplier",
                         type: 'string',
                     },
                     {
                         data: "part",
                         title: "Part No",
                         type: 'string',
                     },
                     {
                         data: (row) => (
                             {
                                 manufacturerCode: row.manufacturerCode,
                                 isRestrictedManufacturer: row.isRestrictedManufacturer
                             }
                         ),
                         name: 'manufacturerCode',
                         title: `Mfr`,
                         type: 'string',
                         className: 'text-wrap text-break',
                         render: (data) => {
                             let escapedManufacturerCode = "";
                             if (data.manufacturerCode) {
                                 const manufacturerCodeText = GlobalTrader.StringHelper.setCleanTextValue(data.manufacturerCode);
                                 escapedManufacturerCode = DataTable.render.text().display(manufacturerCodeText);
                             }
                             return data.isRestrictedManufacturer ? `<p class="m-0" style="background-color: #ff0000; color:#282424f2; background-position: right -1px; !important" title="this manufacturer is restricted use">Restricted Use</p>` : escapedManufacturerCode;
                         }
                     },
                     {
                         data: "dateCode",
                         title: "DC",
                         type: 'string',
                     },
                     {
                         data: "quantity",
                         title: "Quantity",
                         type: 'string',
                     },
                     {
                         data: "formatedPrice",
                         title: "Unit Price",
                         type: 'string',
                     },
                 ]
             }).on('draw.dt', () => {
                 this.sourcingResultTable.columns.adjust();
             }).on('processing.dt', (e, settings, processing) => {
                 this.removeNeutralSortingIcon(this.sourcingResultTable);
             });           

             this.sourcingResultTable.on('select', async (e, dt, type, indexes) => {
                 if (type === 'row') {
                     const selectedRowId = dt.row(indexes).id();
                     this.onSelectedSourceItem(new SourceItemModel(FromSourceTypeConstant.SOURCINGRESULT, selectedRowId));
                 };
             })
         }
         else {
             this.sourcingResultTable.ajax.url(`/api/orders/customer-requirements/${selectedCustomerRequirementId}/sourcing-results`).load();
         }
    }

    removeNeutralSortingIcon(datatable) {
        // Remove neutral sorting icon
        const tableId = datatable.table().node().id;
        $(`#${tableId} thead th`)
            .removeClass('dt-orderable-asc dt-orderable-desc')
            .addClass('position-relative');

        $(`#${tableId} thead th:not(.dt-orderable-none)`)
            .attr('role', 'button');

        $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
    }
}