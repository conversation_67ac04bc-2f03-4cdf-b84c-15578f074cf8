﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPO;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPOStock;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Queries
{
    public class GetSalesOrderLineReportPoStockHandlerTest
    {
        private readonly Mock<IBaseRepository<SalesOrderLineReportPoStockReadModel>> _salesOrderLinePoRepository;
        private readonly Mock<IMapper> _mapper;
        private readonly GetSalesOrderLineReportPoStockHandler _handler;
        private readonly IFixture _fixture = new Fixture();
        public GetSalesOrderLineReportPoStockHandlerTest()
        {
            _salesOrderLinePoRepository = new Mock<IBaseRepository<SalesOrderLineReportPoStockReadModel>>();
            _mapper = new Mock<IMapper>();
            _handler = new GetSalesOrderLineReportPoStockHandler(_salesOrderLinePoRepository.Object, _mapper.Object);
        }
        [Fact]
        public async Task GetSalesOrderLineReportPoStockHandler_ReturnValueTest()
        {
            _salesOrderLinePoRepository.Setup(z => z.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>())).ReturnsUsingFixture(_fixture);
            _mapper.Setup(z => z.Map<IReadOnlyList<SalesOrderLineReportPoStockDto>>(It.IsAny<IReadOnlyList<SalesOrderLineReportPoStockReadModel>>()))
                .ReturnsUsingFixture(_fixture);
            var request = _fixture.Create<GetSalesOrderLineReportPoStockQuery>();
            var result = await _handler.Handle(request, CancellationToken.None);
            Assert.NotNull(result);
            Assert.True(result.Success);
        }
    }
}
