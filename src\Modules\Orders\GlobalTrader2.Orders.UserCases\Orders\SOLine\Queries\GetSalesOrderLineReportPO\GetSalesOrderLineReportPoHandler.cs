﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;
using Microsoft.IdentityModel.Tokens;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPO
{
    public class GetSalesOrderLineReportPoHandler : IRequestHandler<GetSalesOrderLineReportPoQuery, BaseResponse<IReadOnlyList<SalesOrderLineReportPoDto>>>
    {
        private readonly IBaseRepository<SalesOrderLineReportPoReadModel> _salesOrderLinePoRepository;
        private readonly IMapper _mapper;
        public GetSalesOrderLineReportPoHandler(IBaseRepository<SalesOrderLineReportPoReadModel> salesOrderLinePoRepository, IMapper mapper)
        {
            _salesOrderLinePoRepository = salesOrderLinePoRepository;
            _mapper = mapper;
        }
        public async Task<BaseResponse<IReadOnlyList<SalesOrderLineReportPoDto>>> Handle(GetSalesOrderLineReportPoQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IReadOnlyList<SalesOrderLineReportPoDto>>();

            var paramters = new List<SqlParameter>()
            { new SqlParameter("@SalesOrderLineId", SqlDbType.Int) { Value = request.SalesOrderLineId } };
            var queryResult = await _salesOrderLinePoRepository.SqlQueryRawAsync
                ($"{StoredProcedures.Get_All_SalesOrderLine_ReportPO} @SalesOrderLineId", paramters.ToArray());
            if (queryResult.Any())
            {
                response.Data = _mapper.Map<IReadOnlyList<SalesOrderLineReportPoDto>>(queryResult);
            }
            response.Success = true;
            return response;
        }
    }
}
