﻿import { SourceFilterComponent } from './source-filter-component.js?v=#{BuildVersion}#';
import { SourceItemModel } from '../../models/source-item.model.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';
import { FieldType } from '../../../../../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
export class ServiceSourceManager {
    constructor({ onSelectedSourceItem = (data) => { }, globalClientNo }) {
        this.tableFilter = null;
        this.serviceSourceTable = null;
        this.onSelectedSourceItem = onSelectedSourceItem;
        this.globalClientNo = globalClientNo;
        this.serviceSourceUrl = "/api/services/item-search";
        this.pageSize = $(`#add-lines-dialog`).data('default-page-size') || 10;
        this.filterInputs = [
            {
                fieldType: FieldType.TEXT,
                label: 'Name',
                name: 'ServiceName',
                id: 'ServiceName',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            }
        ]
    }

    async initialize() {
        $("#services-source").show();
        await this.initTableFilter();
        this.setDefaultFilter();
        this.initDataTable();
    }

    setDefaultFilter() {
    }

    async initTableFilter() {
        this.tableFilter = new SourceFilterComponent('#services-source-filter-section-wrapper', 'Search for and select the item you would like to use as the source for the new Line and press Continue', {
            inputConfigs: this.filterInputs,
            wrapperClass: 'bg-none m-0 p-0'
        });

        await this.tableFilter.init();
        this.tableFilter.on('applied.mtf', () => {
            this.serviceSourceTable.ajax.url(this.serviceSourceUrl);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.serviceSourceTable, true);
        })
        this.tableFilter.on('cancel.mtf', () => {
            if (window.currentXhr) {
                window.currentXhr.abort();
                if ($('#servicesSourceTbl_processing').is(':visible')) {
                    $('#servicesSourceTbl_processing').hide();
                }
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        })
    }

    initDataTable() {
        this.serviceSourceTable = new DataTable('#servicesSourceTbl', {
            scrollCollapse: true,
            paging: true,
            dataSrc: 'data',
            serverSide: true,
            lengthMenu: [5, 10, 25, 50],
            pageLength: this.pageSize,
            ajax: {
                url: this.tableFilter.hasAnyActiveFilter() ? this.serviceSourceUrl : "",
                type: 'POST',
                contentType: 'application/json',
                beforeSend: (xhr) => {
                    window.currentXhr = xhr;
                },
                data: (data) => {
                    const filterValues = this.tableFilter.getAllValue();
                    return JSON.stringify({
                        serviceName: filterValues.ServiceName.isOn ? filterValues.ServiceName.value : null,
                        globalLoginClientNo: this.globalClientNo,
                        draw: data.draw,
                        index: data.start,
                        size: data.length,
                        sortDir: data.order[0]?.column ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : 1,
                        orderBy: data.order[0]?.column ? data.order[0].column : 1,
                    });
                },
            },
            info: true,
            responsive: true,
            select: {
                style: 'single'
            },
            ordering: true,
            searching: false,
            processing: true,
            columnDefs: [
                { "orderSequence": ["asc", "desc"], "targets": "_all" },
            ],
            language: {
                emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                infoFiltered: "",
                lengthMenu: "_MENU_ per page",
                loadingRecords: "",
            },
            dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                '<"dt-layout-cell dt-layout-end" p >><"clear">',
            rowId: "serviceId",
            order: [[0, "desc"]],
            columns: [
                {
                    data: "serviceName",
                    title: "Service",
                    type: 'string',
                },
                {
                    data: "serviceDescription",
                    title: "Description",
                    type: 'string',
                },
                {
                    data: "formatedCost",
                    title: "Cost",
                    type: 'string',
                },
                {
                    data: "formatedPrice",
                    title: "Unit Price",
                    type: 'string',
                },
            ]
        }).on('draw.dt', () => {
            this.serviceSourceTable.columns.adjust();
        }).on('processing.dt', (e, settings, processing) => {
            this.removeNeutralSortingIcon(this.serviceSourceTable);
            if (processing) {
                // Table is processing (e.g., AJAX call happening)
                this.tableFilter.toggleApplyCancelButtons(false);
            } else {
                // Done processing
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        });

        this.serviceSourceTable.on('select', async (e, dt, type, indexes) => {
            if (type === 'row') {
                const selectedRowId = dt.row(indexes).id();
                this.onSelectedSourceItem(new SourceItemModel(FromSourceTypeConstant.SERVICE, selectedRowId));
            };
        })
    }

    clearTable() {
        $("#services-source").hide();
        if ($('#servicesSourceTbl_processing').is(':visible')) {
            $('#servicesSourceTbl_processing').hide();
        }
        if (this.serviceSourceTable != null) {
            this.serviceSourceTable.rows('.selected').deselect();
            this.serviceSourceTable.ajax.url('').load();
            this.serviceSourceTable.ajax.url(this.serviceSourceUrl);
        }
    }

    doSearch() {
        $("#services-source").show();
        
        if (this.tableFilter.hasAnyActiveFilter()) {
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.serviceSourceTable, true);
        }
    }

    formatDateFilter(dateString) {
        const [day, month, year] = dateString.split("/");
        return `${year}-${month}-${day}T00:00:00.000Z`
    }

    removeNeutralSortingIcon(datatable) {
        // Remove neutral sorting icon
        const tableId = datatable.table().node().id;
        $(`#${tableId} thead th`)
            .removeClass('dt-orderable-asc dt-orderable-desc')
            .addClass('position-relative');

        $(`#${tableId} thead th:not(.dt-orderable-none)`)
            .attr('role', 'button');

        $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
    }
}