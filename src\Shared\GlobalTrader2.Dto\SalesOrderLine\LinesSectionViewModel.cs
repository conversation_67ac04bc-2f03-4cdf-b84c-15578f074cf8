namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class LinesSectionViewModel
    {
        public bool IsReadOnly { get; set; }        
        public bool CanAdd { get; set; }
        public bool CanEdit { get; set; }
        public bool CanPost { get; set; }
        public bool CanUnpost { get; set; }
        public bool CanUnpostAll { get; set; }
        public bool CanDelete { get; set;}
        public bool CanAllocate { get; set; }
        public bool CanClose { get; set; }
        public bool CanCreateIpo { get; set; }
        public bool CanCloneThisLine { get; set; }
        public bool CanConfirm { get; set; }
        public bool CanConfirmAll { get; set; }
        public bool CanEditAll { get; set; }
        public bool CanEccnLog { get; set; }
        public bool CanEditDateRequired { get; set; }
        public bool CanEditPromiseDateAfterCheck { get; set; }
        public bool IsAutoAuthorizeSo { get; set; }
        public string? CompanyName { get; set; }
        public int SalesOrderNumber { get; set; }
        public bool CanAddNewLine { get; set; }
        public bool CanAddReq { get; set; }
        public bool CanAddQuote { get; set; }
        public bool CanAddSO { get; set; }
        public bool CanAddStock { get; set; }
        public bool CanAddService { get; set; }
        public bool CanAddLot { get; set; }
        public int DefaultListPageSize { get; set; }

    }
}