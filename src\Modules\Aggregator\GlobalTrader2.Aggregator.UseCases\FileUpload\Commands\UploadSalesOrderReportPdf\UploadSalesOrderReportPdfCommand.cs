﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.UploadSalesOrderReportPdf
{
    public class UploadSalesOrderReportPdfCommand : IRequest<BaseResponse<string>>
    {
        public required int ClientId { get; set; }
        public required int SalesOrderId { get; set; }
        public required string LoginFullName { get; set; }
        public required string BaseDirectory { get; set; }
        public required int LoginId { get; set; }
    }
}
