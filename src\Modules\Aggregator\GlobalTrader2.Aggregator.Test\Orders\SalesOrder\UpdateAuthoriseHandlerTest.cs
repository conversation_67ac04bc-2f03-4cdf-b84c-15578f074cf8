﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.UploadSalesOrderReportPdf;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyIPO;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifySales;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetSaleOrderMainInfo;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.UpdateAuthorise;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Dto.InternalPurchaseOrder;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.InternalPurchaseOrder.Queries.GetIPOAfterSOChecked;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;
using MediatR;
using Microsoft.Data.SqlClient;
using Moq;

namespace GlobalTrader2.Aggregator.Test.Orders.SalesOrder
{
    public class UpdateAuthoriseHandlerTest
    {
        private readonly Mock<IBaseRepository<object>> _objectRepository;
        private readonly Mock<ISender> _sender;
        private readonly UpdateAuthoriseHandler _handler;
        private readonly IFixture _fixture;
        public UpdateAuthoriseHandlerTest()
        {
            _objectRepository = new Mock<IBaseRepository<object>>();
            _sender = new Mock<ISender>();
            _handler = new UpdateAuthoriseHandler(_objectRepository.Object, _sender.Object);
            _fixture = new Fixture();
        }
        [Fact]
        public async Task UpdateAuthoriseHandler_AuthoriseSuccessTest()
        {
            _objectRepository.Setup(x => x.ExecuteSqlRawAsync(
               It.IsAny<string>(),
               It.IsAny<object[]>()))
                 .Callback<string, object[]>((query, parameters) =>
                 {
                     var sqlParameters = parameters.Cast<SqlParameter>().ToArray();
                     var outputParam = sqlParameters.First(p => p.ParameterName == "@RowsAffected");
                     outputParam.Value = 1;
                 }).ReturnsAsync(1);
            _sender.Setup(m => m.Send(It.IsAny<UploadSalesOrderReportPdfCommand>(), It.IsAny<CancellationToken>()))
             .ReturnsAsync(new BaseResponse<string> { Data = "filePath", Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<NotifySalesCommand>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(new BaseResponse<int> { Data = 1, Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<NotifyIpoCommand>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(new BaseResponse<int> { Data = 1, Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<GetSaleOrderMainInfoQuery>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(new BaseResponse<SaleOrderMainInfoDto> { Data = _fixture.Create<SaleOrderMainInfoDto>(), Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<GetIpoAfterSoCheckedQuery>(), It.IsAny<CancellationToken>()))
         .ReturnsAsync(new BaseResponse<IReadOnlyList<IpoAfterSoCheckeddto>> { Data = _fixture.Create<IReadOnlyList<IpoAfterSoCheckeddto>>(), Success = true }).Verifiable();
            var command = new UpdateAuthoriseCommand()
            {
                SalesOrderId = 1,
                LoginId = 1,
                IsAuthorise = true,
                IsNotify = true,
                IsAllowReadyToShip = false,
                SOAuthoriseSubject = "abc {0}",
                CreateIPOSubject = "New Purchase Order Created",
                Comment = "Test Comment",
                HostUrl = "http://test.com",
                InternalPurchaseOrderUrl = "/InternalPurchaseOrder/Details",
                LoginEmail = "<EMAIL>",
                LoginName = "Test User",
                PurchaseOrderUrl = "/PurchaseOrder/Details",
                ClientId = 101,
                BaseDirectory = "path/to/image"

            };
            var result = await _handler.Handle(command, CancellationToken.None);
            Assert.True(result.Success);
            Assert.True(result.Data);
            _sender.Verify(m => m.Send(It.IsAny<NotifySalesCommand>(), It.IsAny<CancellationToken>()), Times.Once);
            _sender.Verify(m => m.Send(It.IsAny<NotifyIpoCommand>(), It.IsAny<CancellationToken>()), Times.Once);
            _sender.Verify(m => m.Send(It.IsAny<GetSaleOrderMainInfoQuery>(), It.IsAny<CancellationToken>()), Times.Once);
            _sender.Verify(m => m.Send(It.IsAny<GetIpoAfterSoCheckedQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }
        [Fact]
        public async Task UpdateAuthoriseHandler_DeAuthoriseSuccessTest()
        {
            _objectRepository.Setup(x => x.ExecuteSqlRawAsync(
               It.IsAny<string>(),
               It.IsAny<object[]>()))
                 .Callback<string, object[]>((query, parameters) =>
                 {
                     var sqlParameters = parameters.Cast<SqlParameter>().ToArray();
                     var outputParam = sqlParameters.First(p => p.ParameterName == "@RowsAffected");
                     outputParam.Value = 1;
                 }).ReturnsAsync(1);
            _sender.Setup(m => m.Send(It.IsAny<UploadSalesOrderReportPdfCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BaseResponse<string> { Data = "filePath", Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<NotifySalesCommand>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(new BaseResponse<int> { Data = 1, Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<NotifyIpoCommand>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(new BaseResponse<int> { Data = 1, Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<GetSaleOrderMainInfoQuery>(), It.IsAny<CancellationToken>()))
          .ReturnsAsync(new BaseResponse<SaleOrderMainInfoDto> { Data = _fixture.Create<SaleOrderMainInfoDto>(), Success = true }).Verifiable();
            _sender.Setup(m => m.Send(It.IsAny<GetIpoAfterSoCheckedQuery>(), It.IsAny<CancellationToken>()))
         .ReturnsAsync(new BaseResponse<IReadOnlyList<IpoAfterSoCheckeddto>> { Data = _fixture.Create<IReadOnlyList<IpoAfterSoCheckeddto>>(), Success = true }).Verifiable();
            var command = new UpdateAuthoriseCommand()
            {
                SalesOrderId = 1,
                LoginId = 1,
                IsAuthorise = false,
                IsNotify = true,
                IsAllowReadyToShip = false,
                SOAuthoriseSubject = "abc {0}",
                CreateIPOSubject = "New Purchase Order Created",
                Comment = "Test Comment",
                HostUrl = "http://test.com",
                InternalPurchaseOrderUrl = "/InternalPurchaseOrder/Details",
                LoginEmail = "<EMAIL>",
                LoginName = "Test User",
                PurchaseOrderUrl = "/PurchaseOrder/Details",
                ClientId = 101,
                BaseDirectory = "path/to/image"

            };
            var result = await _handler.Handle(command, CancellationToken.None);
            Assert.True(result.Success);
            Assert.True(result.Data);
            _sender.Verify(m => m.Send(It.IsAny<NotifySalesCommand>(), It.IsAny<CancellationToken>()), Times.Never);
            _sender.Verify(m => m.Send(It.IsAny<NotifyIpoCommand>(), It.IsAny<CancellationToken>()), Times.Never);
            _sender.Verify(m => m.Send(It.IsAny<GetSaleOrderMainInfoQuery>(), It.IsAny<CancellationToken>()), Times.Never);
            _sender.Verify(m => m.Send(It.IsAny<GetIpoAfterSoCheckedQuery>(), It.IsAny<CancellationToken>()), Times.Never);
            _sender.Verify(m => m.Send(It.IsAny<UploadSalesOrderReportPdfCommand>(), It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}
