﻿using GlobalTrader2.Aggregator.UseCases.Orders.PurchaseOrders.GetPurchaseOrderMainInfo;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;

using Microsoft.Extensions.Localization;
using System.Globalization;
namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/orders/purchase-orders/details")]
    public class PurchaseOrderDetailsController : ApiBaseController
    {
        private readonly IMediator _mediator;
        private readonly SecurityManager _securityManager;
        private readonly IStringLocalizer<Misc> _miscLocalizer;
        private readonly IStringLocalizer<Status> _statusLocalizer;

        private readonly SessionManager _sessionManager;

        public PurchaseOrderDetailsController(IMediator mediator, SecurityManager securityManager, SessionManager sessionManager, IStringLocalizer<Misc> miscLocalizer, IStringLocalizer<Status> statusLocalizer)
        {
            _mediator = mediator;
            _securityManager = securityManager;
            _sessionManager = sessionManager;
            _miscLocalizer = miscLocalizer;
            _statusLocalizer = statusLocalizer;
        }

        [HttpGet("{purchaseOrderId}/main-info")]
        public async Task<IActionResult> GetPurchaseOrderMainInfo(int purchaseOrderId)
        {
            if (purchaseOrderId <= 0)
            {
                return BadRequest("Invalid purchase order ID.");
            }
            var query = new GetPurchaseOrderMainInfoQuery
            {
                PurchaseOrderId = purchaseOrderId,
                ClientNo = ClientId,
                ClientCurrencyCode = _sessionManager.ClientCurrencyCode,
                ClientCurrencyID = _sessionManager.ClientCurrencyId.GetValueOrDefault(),
                IsPOHub = IsPOHub

            };
            var response = await _mediator.Send(query);
            if (response.Data != null)
            {
                var currency = Functions.FormatCurrency(response.Data.TotalShipInCostVal, CultureInfo.CurrentCulture, _sessionManager.ClientCurrencyCode, 2, true);
                response.Data.TotalShipInCostText = String.Format("({0} {1})", "Recommended Ship In Cost", currency);
                response.Data.DLUPText = LocalizerHelper.FormatDLUP(response.Data.DLUP, response.Data.UpdatedByName ?? "", _miscLocalizer, CultureInfo.CurrentCulture);
                response.Data.Status = response.Data.StatusNo.HasValue ? _statusLocalizer[((PurchaseOrderStatus)response.Data.StatusNo).ToString()] : "";
            }
            return Ok(response);
        }
    }
}
