﻿export class CollapsibleBoxHelper {
    static setCollapsibleBoxLoading($boxContainerSelector, isLoading) {
        const $refreshButton = $boxContainerSelector.find(".collapsible-box-refresh-button");
        const $boxTitle = $boxContainerSelector.find(".collapsible-title");

        if (isLoading) {
            $refreshButton.addClass("disabled");
            //set box title loading
            if ($boxContainerSelector.find(".spinner-loader")[0]) {
                return;
            }
            $(`<div class="spinner-loader ms-1"></div>`).insertAfter($boxTitle);

            //set content box loading
            if ($boxContainerSelector.find(".text-loader")[0]) {
                return;
            }

            $boxContainerSelector.find(`.fieldset-content-wrapper`).hide();
            $(`<div class="text-loader m-auto"></div>`).insertAfter($boxContainerSelector.find(`.fieldset-content-wrapper`)[0]);
        } else {
            $refreshButton.removeClass("disabled");
            $boxContainerSelector.find(".spinner-loader").remove();

            $boxContainerSelector.find(`.text-loader`).remove();
            $boxContainerSelector.find(`.fieldset-content-wrapper`).show();
        }
    }

    static updateRowsCount($boxContainerSelector, count) {
        $boxContainerSelector.find('.rowsCount').text(count);
    }
}