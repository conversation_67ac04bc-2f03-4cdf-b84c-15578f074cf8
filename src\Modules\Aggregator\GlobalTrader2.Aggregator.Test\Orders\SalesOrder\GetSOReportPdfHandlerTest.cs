﻿using System.IO;
using System.Reflection;
using AutoFixture;
using AutoFixture.AutoMoq;
using AutoMapper;
using GlobalTrader2.Aggregator.UseCases.Account.LoginForSendEmai.LoginForSendEmai.Queries;
using GlobalTrader2.Aggregator.UseCases.Account.LoginForSendEmai.Queries.Dtos;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetSOReportPdf;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.Address;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetCurrencyRateCurrentAtDate;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportManualStock;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPO;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPOStock;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportShipped;
using MediatR;
using Microsoft.VisualStudio.TestPlatform.PlatformAbstractions.Interfaces;
using Moq;

namespace GlobalTrader2.Aggregator.Test.Orders.SalesOrder
{
    public class GetSOReportPdfHandlerTest
    {
        private readonly Mock<IRazorViewToStringService> _razorViewToStringService;
        private readonly Mock<IBaseRepository<SalesOrderForPrintReadModel>> _salesOrderForPrintRepository;
        private readonly Mock<IMapper> _mapper;
        private readonly Mock<ISender> _sender;
        private readonly Mock<IPdfService> _pdfService;
        private readonly Mock<IBaseRepository<AddressSelect>> _addressRepository;
        private const string base64Format = "data:image/jpg;base64, {0}";
        private const string allocatedImagePath = "allocated.gif";
        private const string shippedImagePath = "shipped.gif";
        private const string ihsPartstatusPath = "ihspartstatuspng.png";
        private const string hazardousPath = "Hazardous.png";
        private readonly GetSOReportPdfHandler _handler;
        private readonly IFixture _fixture = new Fixture();
        public GetSOReportPdfHandlerTest()
        {
            _razorViewToStringService = new Mock<IRazorViewToStringService>();
            _salesOrderForPrintRepository = new Mock<IBaseRepository<SalesOrderForPrintReadModel>>();
            _mapper = new Mock<IMapper>();
            _sender = new Mock<ISender>();
            _pdfService = new Mock<IPdfService>();
            _addressRepository = new Mock<IBaseRepository<AddressSelect>>();
            _handler = new GetSOReportPdfHandler(
                _salesOrderForPrintRepository.Object
                , _razorViewToStringService.Object
                , _mapper.Object
                , _sender.Object
                , _pdfService.Object
                , _addressRepository.Object);
        }
        [Fact]
        public async Task GetSOReportPdfHandler_ConvertPdfSuccessTest()
        {
            // Arrange
            var expectedText = "Rendered HTML";
            var query = new GetSOReportPdfQuery
            {
                SalesOrderId = 1,
                ClientId = 1,
                LoginFullName = "Test User",
                BaseDirectoryPath = "testImagePath",
            };
            var salesOrderForPrint = _fixture.Create<IReadOnlyList<SalesOrderForPrintReadModel>>();
            var salesOrderForPrintDto =_fixture.Create<SalesOrderForPrintDto>();
            _salesOrderForPrintRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
               .ReturnsAsync(salesOrderForPrint);

            _mapper.Setup(m => m.Map<SalesOrderForPrintDto>(It.IsAny<SalesOrderForPrintReadModel>()))
                .Returns(salesOrderForPrintDto);
            _mapper.Setup(m => m.Map<AddressDto>(It.IsAny<AddressSelect>()))
              .ReturnsUsingFixture(_fixture);

            _mapper.Setup(m => m.Map<SalesOrderLineForSOReportDto>(It.IsAny<AllOpenClosedSalesOrderLineDetailsDto>()))
             .ReturnsUsingFixture(_fixture);
            _razorViewToStringService.Setup(s => s.RenderViewToStringAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(expectedText).Verifiable();
         
            _pdfService.Setup(p => p.CreateSOReportFromPdfAsync(It.IsAny<string>()))
                .ReturnsUsingFixture(_fixture).Verifiable();
            _sender.Setup(z => z.Send(It.IsAny<GetCurrencyRateCurrentAtDateQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsUsingFixture(_fixture);
            _sender.Setup(z => z.Send(It.IsAny<GetSalesOrderLineReportShippedQuery>(), It.IsAny<CancellationToken>()))
               .ReturnsUsingFixture(_fixture);
            _sender.Setup(z => z.Send(It.IsAny<GetSalesOrderLineReportManualStockQuery>(), It.IsAny<CancellationToken>()))
               .ReturnsUsingFixture(_fixture);
            _sender.Setup(z => z.Send(It.IsAny<GetSalesOrderLineReportPoQuery>(), It.IsAny<CancellationToken>()))
               .ReturnsUsingFixture(_fixture);
            _sender.Setup(z => z.Send(It.IsAny<GetSalesOrderLineReportPoStockQuery>(), It.IsAny<CancellationToken>()))
               .ReturnsUsingFixture(_fixture);
            // Act
            var result = await _handler.Handle(query, CancellationToken.None);
            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            _razorViewToStringService.Verify(s => s.RenderViewToStringAsync(It.IsAny<string>(), It.IsAny<object>()),Times.Once);

            _pdfService.Verify(p => p.CreateSOReportFromPdfAsync(It.IsAny<string>()),Times.Once);
        }
    }
}
