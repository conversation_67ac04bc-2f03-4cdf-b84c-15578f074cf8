﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Dto.PurchaseOrder
{
    public class PurchaseOrderForPageDto
    {
        public int PurchaseOrderId { get; set; }
        public int? PurchaseOrderNumber { get; set; }
        public int? ClientNo { get; set; }
        public int? CompanyNo { get; set; }
        public int? StatusNo { get; set; }
        public bool? Closed { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyNameForSearch { get; set; }
        public bool? IsPDFAvailable { get; set; }
        public int? TeamNo { get; set; }
        public int? DivisionNo { get; set; }
        public int? Buyer { get; set; }
        public int? IPOClientNo { get; set; }
        public bool? IsPOHub { get; set; }
        public string? ClientName { get; set; }
        public string? ClientBaseCurrencyCode { get; set; }
        public bool? IsPORPDFAvailable { get; set; }
    }
}
