﻿import { loadCSS } from '../../helper/load-css.helper.js?v=#{BuildVersion}#';
import '../../helper/string-helper.js?v=#{BuildVersion}#';
import { TextFilterHelper } from '../../helper/text-filter-helper.js?v=#{BuildVersion}#';
import { EventEmitter } from '../base/event-emmiter.js?v=#{BuildVersion}#';
export class SearchSelectComponent extends EventEmitter {
    invalidClassName = "is-invalid";

    constructor(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch = 2, filterSearchIndex = null, numberOfRowToShow = 10) {
        super();
        loadCSS('/js/components/search-select/search-select.css');
        this.debouncedLoadResults = this.debounce(this.loadResults, 500);
        this.searchInputId = searchInputId;
        this.searchInput = document.getElementById(searchInputId);
        this.mode = mode;
        this.apiKeySearch = apiKeySearch;
        this.apiURL = apiURL;
        this.valueHiddenInput = document.getElementById(inputHiddenValueId);
        this.charsToSearch = charsToSearch;
        this.searchInputValue = [];
        this.selectedLabel = [];
        this.filterSearchIndex = filterSearchIndex;
        this.numberOfRowToShow = numberOfRowToShow;
        this.maxResultHeight = 0;
        this.isSearchInputFocused = document.activeElement === this.searchInput;
        // Create UI elements
        this.createSearchSelection();
        this.createResultsWrapper();

        // Initialize ResizeObserver
        this.resizeObserver = new ResizeObserver(entries => {
            entries.forEach(entry => {
                this.wrapper.style.minWidth = `${entry.contentRect.width + 26}px`; // Adjusted width
            });
        });
        this.resizeObserver.observe(this.searchInput);

        // Bind event listeners
        this.attachEventListeners();

        this.observeValidationChanges();

        const scrollableDialog = this.searchInput.closest('.ui-dialog-content');
        if (scrollableDialog) {
            scrollableDialog.addEventListener('scroll', () => this.adjustDropdownPosition());
        }

    }

    observeValidationChanges() {
        const observer = new MutationObserver((mutationsList) => {
            mutationsList.forEach(mutation => {
                if (mutation.attributeName === "class") {
                    this.syncValidationState();
                }
            });
        });

        observer.observe(this.valueHiddenInput, { attributes: true, attributeFilter: ["class"] });

    }

    syncValidationState() {
        if (this.valueHiddenInput.classList.contains("is-invalid")) {
            this.searchInput.classList.add("is-invalid");
        } else {
            this.searchInput.classList.remove("is-invalid");
        }
    }

    createSearchSelection() {
        this.searchSelection = this.createElement(`
            <div id="searchSection-${this.searchInputId}" class="${this.mode}-mode">
                <div id="selectedItems-${this.searchInputId}" class="selected-items"></div>
            </div>
        `);
        this.searchInput.insertAdjacentElement('beforebegin', this.searchSelection);
        this.searchSelection.style.display = 'none';
        this.selectedItemsDiv = this.searchSelection.querySelector(`#selectedItems-${this.searchInputId}`);
    }

    createResultsWrapper(position = 'fixed') {
        this.wrapper = this.createElement(`
            <div id="result-wrapper-${this.searchInputId}" class="position-${position} search-select-result-wrapper z-index-2">
                <div class="d-flex align-items-center justify-between search-select-message-wrapper">
                    <div id="message-${this.searchInputId}" class="search-select-message p-1"></div>
                    <button type="button" class="btn-close" aria-label="Close"></button>
                </div>
                <div id="results-${this.searchInputId}" class="search-select-results d-none" tabindex="-1"></div>
            </div>
        `);
        this.searchInput.insertAdjacentElement('afterend', this.wrapper);
        this.wrapper.style.display = 'none';
        this.messageDiv = this.wrapper.querySelector(`#message-${this.searchInputId}`);
        this.resultsDiv = this.wrapper.querySelector(`#results-${this.searchInputId}`);

        // 
        document.getElementById(`result-wrapper-${this.searchInputId}`).addEventListener('keydown', (event) => {
            if (event.key === "Tab" && this.resultsDiv.innerHTML === '') {
                this.closeSearchInput();
            }
        });

        // Close button event
        this.wrapper.querySelector('.btn-close').addEventListener('click', () => this.closeSearchInput());
    }

    closeSearchInput() {
        this.wrapper.style.display = 'none';
    }

    adjustDropdownPosition() {
        const dialog = this.searchInput.closest('.ui-dialog');

        if (!dialog) return; // fallback if not inside dialog

        const inputRect = this.searchInput.getBoundingClientRect();
        const dialogRect = dialog.getBoundingClientRect();

        const spaceAbove = inputRect.top - dialogRect.top;
        const spaceBelow = window.innerHeight - inputRect.bottom;

        if (spaceBelow < this.maxResultHeight && spaceAbove > this.maxResultHeight) {
            this.wrapper.classList.add('above');
            this.wrapper.style.top =
                `${spaceAbove - this.maxResultHeight - this.searchInput.offsetHeight + this.convertRemToPixels(0.2)}px`;//NOTE: 0.2rem is from focused input box shadow
            this.wrapper.style.bottom = 'auto'; // optional fine-tune
        } else {
            this.wrapper.classList.remove('above');
            this.wrapper.style.top = `${spaceAbove + this.searchInput.offsetHeight}px`;
            this.wrapper.style.bottom = 'auto'; // optional fine-tune
        }

        // Horizontally align
        this.wrapper.style.minWidth = `${this.searchInput.offsetWidth}px`;
    }

    attachEventListeners() {
        this.searchInput.addEventListener('input', () => this.handleSearchInput());
        this.searchInput.addEventListener('selectItem', async (event) => await this.selectItem(event.detail));

        this.searchInput.addEventListener('focus', (event) => {
            this.adjustDropdownPosition();
            if (!event.isTrusted) return; // Prevents programmatic focus from triggering
            this.isSearchInputFocused = true;
            this.handleSearchInput();
        });

        this.searchInput.addEventListener('blur', () => {
            this.isSearchInputFocused = false;
        });

        this.searchInput.addEventListener('keydown', (event) => {
            if (event.key === "ArrowDown") {
                event.preventDefault();
                this.focusFirstOption();
            }
        });

        document.addEventListener('mousedown', (event) => {
            const isClickInside = this.wrapper.contains(event.target) || this.searchInput.contains(event.target);
            if (!isClickInside) {
                this.closeSearchInput();
            }
        });
    }

    focusFirstOption() {
        const firstOption = this.resultsDiv.querySelector('.list-group-item');
        if (firstOption) {
            firstOption.focus();
        }
    }


    handleSearchInput() {
        let query = this.searchInput.value.trim();
        if (query.length < this.charsToSearch) {
            this.showMessage(`Type ${this.charsToSearch} characters to search`);
        } else if (query.length >= this.charsToSearch) {
            this.showMessage('Loading...');
            this.debouncedLoadResults(this.setFilterValue(query));
        }
    }

    async loadResults(query) {
        // type of data = [{label: string; value: any}]
        try {
            const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() }
            let response = await GlobalTrader.ApiClient.postAsync(this.apiURL, { [this.apiKeySearch]: query }, header)

            // return if the input length is not enough to show results || not focusing
            if (this.searchInput.value.trim().length < this.charsToSearch || !this.isSearchInputFocused) return;

            const data = response.data;
            this.showMessage(this.getResultFoundMessage(data.length));
            this.renderResults(data);
        } catch (error) {
            console.error("Error loading results:", error);
        }
    }

    showMessage(message) {
        this.wrapper.style.display = 'block';
        this.messageDiv.textContent = message;
        this.clearResults();
    }

    clearResults() {
        this.resultsDiv.innerHTML = '';
        this.resultsDiv.classList.add('d-none');
        this.adjustResultHeight();
        this.adjustDropdownPosition();
    }

    renderResults(data) {
        if (!data || data.length === 0) {
            return;
        }
        this.resultsDiv.innerHTML = '';
        this.resultsDiv.classList.remove('d-none');
        data.forEach(result => {
            const resultItem = this.createResultItem(result);
            this.resultsDiv.appendChild(resultItem);
        });
        this.adjustResultHeight();
        this.adjustDropdownPosition(); // ← Adjust on every open
    }

    adjustResultHeight() {
        const resultItems = this.resultsDiv.children;
        let totalHeight = 0;

        for (let i = 0; i < Math.min(this.numberOfRowToShow, resultItems.length); i++) {
            totalHeight += resultItems[i].offsetHeight;
        }

        this.maxResultHeight = totalHeight + 12; // padding and border

        this.resultsDiv.style.maxHeight = this.maxResultHeight + "px";
    }

    createResultItem(result) {
        const resultItem = document.createElement('div');
        resultItem.className = 'list-group-item list-group-item-action p-1';
        resultItem.tabIndex = 0;
        resultItem.textContent = GlobalTrader.StringHelper.setCleanTextValue(result.label);
        resultItem.style.cursor = 'pointer';
        resultItem.addEventListener("mousedown", (event) => {
            event.preventDefault(); // Prevent focus loss
            this.handleResultSelection(result);
        });

        resultItem.addEventListener("keydown", (event) => {
            if (event.key === "Enter") {
                this.handleResultSelection(result);
            } else if (event.key === "ArrowDown") {
                event.preventDefault();
                this.focusNextResultItem(resultItem);
            } else if (event.key === "ArrowUp") {
                event.preventDefault();
                this.focusPreviousResultItem(resultItem);
            } else if (event.key === "Tab") {
                this.closeSearchInput();
            }
        });
        return resultItem;
    }

    focusNextResultItem(currentItem) {
        const nextItem = currentItem.nextElementSibling;
        if (nextItem) {
            nextItem.focus();
        }
    }

    focusPreviousResultItem(currentItem) {
        const prevItem = currentItem.previousElementSibling;
        if (prevItem) {
            prevItem.focus();
        } else {
            this.searchInput.focus(); // Return focus to input if at the first item
        }
    }

    handleResultSelection(item) {
        this.selectItem(item);
        if (this.mode === 'multiple') {
            this.searchInput.focus();
        }
    }

    async selectItem(item) {
        this.wrapper.style.display = 'none';
        this.searchSelection.style.display = 'block';

        if (this.mode === 'single') {
            await this.handleSingleSelection(item);
        } else {
            await this.handleMultipleSelection(item);
        }

        this.resetSearch();

        this.triggerChangeEvent();
    }

    resetSearch() {
        this.searchInput.value = '';
        this.clearResults();
    }

    async handleSingleSelection(item) {
        this.searchInputValue = [item.value];
        this.selectedLabel = [item.label]
        this.valueHiddenInput.value = this.searchInputValue[0];
        this.searchInput.classList.add('d-none');

        this.selectedItemsDiv.innerHTML = '';
        this.selectedItemsDiv.appendChild(await this.createSelectedItem(item));
    }

    async handleMultipleSelection(item) {
        if (!this.searchInputValue) {
            this.searchInputValue = [];
            this.selectedLabel = [];
        }

        if (!this.isItemDuplicated(item.value)) {
            this.searchInputValue.push(item.value);
            this.selectedLabel.push(item.label);
            this.selectedItemsDiv.appendChild(await this.createSelectedItem(item));
            this.valueHiddenInput.value = JSON.stringify(this.searchInputValue);
        }
    }

    isItemDuplicated(data) {
        return this.searchInputValue.some(item =>
            item === data
        );
    }

    markInvalidSelectedItems(invalidLabels) {
        const selectedItems = this.selectedItemsDiv.querySelectorAll('.selected-item');
        let isInvalid = false;
        selectedItems.forEach(item => {
            const label = item.textContent.trim(); // Get the text of the selected item

            if (invalidLabels.includes(label)) {
                isInvalid = true;
                item.classList.add("invalid"); // Add invalid class if it matches
            } else {
                item.classList.remove("invalid"); // Remove invalid class if no longer invalid
            }
        });

        isInvalid ? this.searchInput.classList.add("is-invalid") : this.searchInput.classList.remove("is-invalid");
    }

    async createSelectedItem(item) {
        const selectedItem = document.createElement("span");
        selectedItem.className = "selected-item";
        selectedItem.innerHTML = `<span class="selected-item-content">${item.label}</span> <button type="button" class="btn-close" aria-label="Close"></button>`;

        // Add event listener for remove button
        selectedItem.querySelector(".btn-close").addEventListener("click", () => {
            this.removeItem(selectedItem, item);
        });

        return selectedItem;
    }

    removeItem(selectedItem, itemToRemove) {
        this.searchInputValue = this.searchInputValue.filter(value => value !== itemToRemove.value);
        this.selectedLabel = this.selectedLabel.filter(value => value !== itemToRemove.label);
        selectedItem.remove();
        this.valueHiddenInput.value = this.searchInputValue.length > 0 ? JSON.stringify(this.searchInputValue) : '';

        if (!this.searchInputValue.length) {
            this.searchInput.value = '';
            this.searchInput.classList.remove('d-none');
            $(`#searchSection-${this.searchInputId}`).hide();
            $(`#selectedItems-${this.searchInputId}`).html('');
            $(`#message-${this.searchInputId}`).text('');
            $(`#results-${this.searchInputId}`).html('');
            $(`#result-wrapper-${this.searchInputId}`).hide();
            this.searchInput.focus();
        }

        this.triggerChangeEvent();
    }

    resetSearchSelect(refocus = true, triggerChange = true) {
        this.searchInput.value = ''; // Clear input field
        this.valueHiddenInput.value = ''; // Clear hidden input
        this.searchInputValue = []; // Reset stored values
        this.selectedLabel = [];

        // Hide selected items
        this.selectedItemsDiv.innerHTML = '';
        this.searchSelection.style.display = 'none';

        // Clear and hide results
        this.clearResults();
        this.wrapper.style.display = 'none';

        // Remove invalid class if present
        this.searchInput.classList.remove("is-invalid");
        this.valueHiddenInput.classList.remove("is-invalid");

        // Ensure input is visible
        this.searchInput.classList.remove('d-none');

        // Refocus on input if necessary
        if (refocus) {
            this.searchInput.focus();
        }
        if (triggerChange) {
            this.triggerChangeEvent();
        }
    }

    createElement(html) {
        const template = document.createElement('template');
        template.innerHTML = html.trim();
        return template.content.firstChild;
    }

    displaySearchSelectErrorBorder() {
        this.searchInput.classList.add(invalidClassName);
    }

    removeSearchSelectErrorBorder() {
        this.searchInput.classList.remove(invalidClassName);
    }

    setFilterSearchIndex(value) {
        this.filterSearchIndex = value;
    }

    setFilterValue(value) {
        if (this.filterSearchIndex === null) { return value; }
        return TextFilterHelper.getFilterValue(this.filterSearchIndex, value);
    }

    getResultFoundMessage(numberOfResults) {
        return `${numberOfResults} result(s)`;
    }

    debounce(func, delay) {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    triggerChangeEvent() {
        this.trigger('change', {
            searchValue: this.searchInput.value,
            hiddenValue: this.valueHiddenInput.value
        })
    }

    convertRemToPixels(rem) {
        return rem * parseFloat(getComputedStyle(document.documentElement).fontSize);
    }
}
