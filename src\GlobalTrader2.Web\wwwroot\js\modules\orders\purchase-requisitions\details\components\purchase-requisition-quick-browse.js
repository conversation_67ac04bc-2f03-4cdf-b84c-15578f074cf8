﻿import { QuickBrowserComponent } from '../../../../../components/quick-browser/quick-browser.component.js?v=#{BuildVersion}#';
import { QuickBrowserEvents } from '../../../../../components/quick-browser/constants/quick-browser.constants.js?v=#{BuildVersion}#';
import { FieldType } from '../../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { TextFilterHelper } from "../../../../../helper/text-filter-helper.js?v=#{BuildVersion}#";
import { NumberType } from '../../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#';

$(async () => {
    const quickBrowseManager = new PurchaseRequisitionBrowseManager();
    $("#left-nugget-PurchaseRequisitionQuickBrowse").on("accordionbeforeactivate", async (event, ui) => {
        if (quickBrowseManager.quickBrowseFilter == null) {
            quickBrowseManager.setLoadingQuickBrowse(true);
            await quickBrowseManager.initialize();
            quickBrowseManager.setLoadingQuickBrowse(false);
        }
    });

});

class PurchaseRequisitionBrowseManager {
    constructor() {
        this.filterStates = JSON.parse($('#purchase-requisition-quick-browser-component').attr("data-states"));
        this.quickBrowseConfig = JSON.parse($('#purchase-requisition-quick-browser-component').attr("data-config"));
        this.pageSize = $('#purchaseRequisitionQuickBrowseTbl').data('default-page-size') || 10;
        this.defaultPageIndex = $('#purchaseRequisitionQuickBrowseTbl').data('default-page-index');
        this.sortIndex = $('#purchaseRequisitionQuickBrowseTbl').data('default-order-by') || 1;
        this.sortDirection = $('#purchaseRequisitionQuickBrowseTbl').data('default-sort-dir') || window.constants.sortASC;
        this.quickBrowseFilter = null;
        this.quickBrowseTable = null;
        this.filterInputs = [
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseViewLevel}`,
                name: 'ViewLevel',
                id: 'ViewLevel',
                value: '',
                options: {
                    serverside: false,
                    endpoint: `lists/view-levels/${this.quickBrowseConfig.enmPageId}`,
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                    placeholder: null
                },
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowsePartNo}`,
                name: 'Part',
                id: 'Part',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseCompany}`,
                name: 'CMName',
                id: 'CMName',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseContact}`,
                name: 'Contact',
                id: 'Contact',
                value: '',
            },

            {
                fieldType: FieldType.NUMBER,
                label: `${window.localizedStrings.quickBrowseSalesOrder}`,
                name: 'SONo',
                id: 'SONo',
                value: '',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true
                },
                extraPros: {
                    numberType: NumberType.INT
                },

            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseSalesperson}`,
                name: 'Salesman',
                id: 'Salesman',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/employee',
                    valueKey: 'loginId',
                    textKey: 'employeeName',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },

            {
                fieldType: FieldType.DATE,
                label: `${window.localizedStrings.quickBrowseDateOrderedFrom}`,
                name: 'DateOrderedFrom',
                id: 'DateOrderedFrom',
                value: '',
            },

            {
                fieldType: FieldType.DATE,
                label: `${window.localizedStrings.quickBrowseDateOrderedTo}`,
                name: 'DateOrderedTo',
                id: 'DateOrderedTo',
                value: '',
            },


        ];
    }

    async initialize() {
        this.initQuickBrowser();
    }

    async initQuickBrowser() {
        this.quickBrowseFilter = new QuickBrowserComponent('#purchase-requisition-quick-browser-component', {
            inputConfigs: this.filterInputs
        });
        this.quickBrowseFilter.init();
        this.quickBrowseFilter.setLockOrUnlockFilter(this.quickBrowseConfig.initSaveDataListState);

        Object.values(this.filterStates).forEach(input => {
            if (input.isOn && input.isShown && input.value && input.name) {
                const currentInput = this.toggleFilterInput(true, input.name);
                if (currentInput) {
                    switch (input.fieldType) {
                        case FieldType.TEXT:
                            currentInput.setValue(TextFilterHelper.getFilterValue(input.searchType, input.value));
                            break;
                        case FieldType.NUMBER:
                            currentInput.setState(input);
                            break;

                        default:
                            currentInput.setValue(input.value);
                            break;
                    }
                }
            }
        });

        this.quickBrowseFilter.on(QuickBrowserEvents.SEARCH, (e) => {
            if (this.quickBrowseTable) {
                GlobalTrader.Helper.reloadPagingDatatableServerSide(this.quickBrowseTable, true);
            }
            else {
                $('#purchaseRequisitionQuickBrowseTbl').removeClass("d-none");
                this.initDataTable();
            }
        });
    }

    initDataTable() {
        this.quickBrowseTable = $('#purchaseRequisitionQuickBrowseTbl')
            .DataTable({
                serverSide: true,
                ajax:
                {
                    url: '/api/orders/purchase-requisition/list',
                    type: 'POST',
                    contentType: 'application/json',
                    beforeSend: (xhr) => {
                    },
                    error: function (xhr, status, error) {
                        console.log("AJAX Error: ", status, error);
                    },
                    data: (data) => {
                        let sortDir = data.order.length !== 0 ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : this.sortDirection;
                        const filtersData = this.quickBrowseFilter.getDisplayedFilterValues().filter((input) => input.name != 'ViewLevel')
                            .map((filter) => {
                            if (filter.fieldType === FieldType.NUMBER) {
                                return { ...filter, lo: filter.low };
                            }
                            return filter;
                        });
                        const viewLevelInput = this.quickBrowseFilter.getInputElementByName('ViewLevel');
                        return JSON.stringify({
                            draw: data.draw,
                            start: data.start,
                            length: data.length,
                            sortDir: sortDir,
                            viewLevel: viewLevelInput.getValue().value,
                            orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex,
                            filters: filtersData,
                            saveStates: this.quickBrowseFilter.isLockFilter
                        });
                    },
                },
                info: true,
                scrollCollapse: true,
                responsive: true,
                select: false,
                displayStart: this.defaultPageIndex * this.pageSize,
                paging: true,
                ordering: true,
                order: [[0, GlobalTrader.SortHelper.getSortDirNameById(this.sortDirection)]],
                columnDefs:
                    [
                        { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
                    ],
                searching: false,
                pageLength: this.pageSize,
                lengthMenu: [5, 10, 25, 50],
                bLengthChange: false,
                dom: "<'dt-layout-start text-center'r>t<'dt-layout-cell text-center'ip>",
                language:
                {
                    emptyTable: `<i>${window.localizedStrings.noMatches}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noMatches}</i>`,
                    infoFiltered: "",
                    lengthMenu: "_MENU_ per page",
                    info: "Page _PAGE_ of _PAGES_",
                    infoEmpty: "",
                    loadingRecords: ""
                },
                processing: true,
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                columns:
                    [

                        {
                            data: (row) => ({
                                salesOrderLineId: row.salesOrderLineId,
                                salesOrderNumber: row.salesOrderNumber,
                                partNo: row.partNo,
                                imageUrl: row.rohsInfo.imageUrl,
                                companyName: row.companyName
                            }),
                            type: 'string',
                            className: 'text-wrap text-break',
                            title: "Purchase Requisition",
                            render: (data, type, row) => {
                                let salesOrderLineId = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.salesOrderLineId)));
                                let salesOrderNumber = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.salesOrderNumber)));
                                let partNo = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.partNo)));
                                let imageUrl = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.imageUrl)));
                                let companyName = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.companyName)));
                                let imgTag = "";
                                if (imageUrl) {
                                    imgTag = `<img src=${imageUrl}>`;
                                }
                                let content = `${salesOrderNumber}/${salesOrderLineId} - ${companyName} x ${partNo} ${imgTag}`
                                if (content.trim() == '') content = row.salesOrderLineId;
                                const url = `${GlobalTrader.PageUrlHelper.Get_Url_PurchaseRequisition(data.salesOrderLineId)}`;
                                return `<span><a class="dt-hyper-link" href="${url}">${content}</a></span>`;
                            }
                        }
                    ],
                rowId: 'salesOrderLineId',
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                },
            })
            .on('preXhr.dt', () => {
            })
            .on('draw.dt', () => {
                this.quickBrowseTable.columns.adjust();

                // Remove neutral sorting icon
                const tableId = this.quickBrowseTable.table().node().id;
                $(`#${tableId} thead th`)
                    .removeClass('dt-orderable-asc dt-orderable-desc')
                    .addClass('position-relative');

                $(`#${tableId} thead th:not(.dt-orderable-none)`)
                    .attr('role', 'button');

                $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
            });
    }

    toggleFilterInput(isShow, fieldName) {
        const currentInput = this.quickBrowseFilter.getInputElementByName(fieldName);
        if (currentInput) {
            currentInput.invisible = !isShow;
            isShow ? currentInput.show() : currentInput.hide();

            const currentOption = this.quickBrowseFilter.getOptionByName(fieldName);
            isShow ? currentOption.addClass('selected') : currentOption.removeClass('selected');
            currentOption.find("img").attr(
                "src",
                `/img/icons/${isShow ? 'check-green.svg' : 'xmark-red.svg'}`
            );
        }
        return currentInput;
    }

    setLoadingQuickBrowse(isLoading) {
        if (isLoading) {
            $('#purchase-requisition-quick-browser-section').prepend(`<div id = "purchase-requisition-loading-quick-browse-content" class= "text-loader m-auto" ></div>`);
        } else {
            $(`#purchase-requisition-loading-quick-browse-content`).remove();
            $(`#purchase-requisition-quick-browser-component`).removeClass("d-none");
        }
    }

} 