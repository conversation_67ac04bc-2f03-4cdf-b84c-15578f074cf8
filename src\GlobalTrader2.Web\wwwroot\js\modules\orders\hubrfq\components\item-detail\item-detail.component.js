import { PageSearchSelectComponent } from "../../../../../components/search-select/page-search-select.component.js?v=#{BuildVersion}#";
import { ROHS_STATUSES } from "../../../../../config/rohs-config.js?v=#{BuildVersion}#";
import { ButtonHelper } from "../../../../../helper/button-helper.js?v=#{BuildVersion}#";
import { IhsHelper } from "../../../../../helper/ihs-helper.js?v=#{BuildVersion}#";
import { ProductHelper } from "../../../../../helper/product-helper.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../../helper/rohs-helper.js?v=#{BuildVersion}#";

export class ItemDetailComponent {
    constructor() {
        this._id = null;
        this._currentData = null;
        this._currentResponse = null;
        this.lyticaManufacturerRefSearchSelect = null;
    }

    show() {
        $('#hubrfq-item-detail').removeClass('d-none');
    }

    clearUI() {
        this._cleanupSearchSelect();
        const $container = $('#hubrfq-item-detail');
        $container.find('span[name]').text('');
        $container.find('input[type="checkbox"]').prop('checked', false);
        $container.find('input[type="text"]').val('');
        $container.addClass('d-none');
    }

    async setId(newId) {
        if (this._id !== newId) {
            this._id = newId;
            await this.onIdChanged();
        }
    }

    async onIdChanged() {
        this._currentData = null;

        if (this._id) {
            await this.fetchAndBindData(this._id);
        } else {
            this.clearUI();
        }
    }

    getCurrentResponse() {
        return this._currentResponse;
    }

    async fetchAndBindData(id) {
        try {
            this.setDetailLoading(true);

            this._cleanupSearchSelect();

            this._currentResponse = await GlobalTrader.ApiClient.getAsync(`/orders/bom/item-details/${id}`);
            this._currentData = this._currentResponse.data;
            this.bindData();
            this.setDetailLoading(false);
            this.show();
        } catch (error) {
            console.error('Error fetching data for ID:', id, error);
            this.setDetailLoading(false);
        }
    }

    async reload() {
        if (this._id) {
            await this.fetchAndBindData(this._id);
        }
    }

    bindData() {
        if (!this._currentData) return;
        this.bindColumn1(this._currentData);
        this.bindColumn2(this._currentData);
        this.bindColumn3(this._currentData);
    }

    bindColumn1(data) {
        if (!data) return;
        const $col1 = this._getPartDetailColumn1();
        $col1.find("[name='quantity']").text(data.quantity);
        $col1.find("[name='partNo']").text(data.part);
        if (data.isPDFAvailable) {
            if (data.stockAvailableDetail && data.stockAvailableDetail.length > 0) {
                this._showStockDetails(data, $col1);
                // Show PDF 
                $col1.find("[name='showPDF']").show();
            } else {
                $col1.find("[name='stockAlert']").hide();
                $col1.find("[name='showPDF']").hide();
            }
        } else {
            if (data.stockAvailableDetail && data.stockAvailableDetail.length > 0) {
                this._showStockDetails(data, $col1);
            } else {
                $col1.find("[name='stockAlert']").hide();
            }
        }
        if (data.isAs6081Required === "Yes") {
            $col1.find("[name='as6081']").text('');
            const span = document.createElement("span");
            span.textContent = window.localizedStrings.yes;
            span.style.backgroundColor = "yellow";
            $col1.find("[name='as6081']").get(0).appendChild(span);
        } else {
            $col1.find("[name='as6081']").text(window.localizedStrings.no);
        }
        $col1.find("[name='rohs']").html(this._writeRohs(data.rohs));
        $col1.find("[name='cusPartNo']").text(data.customerPart);
        if (data.manufacturerNo && data.manufacturerName) {
            $col1.find("[name='manufacturer']").html(ButtonHelper.nubButton_Manufacturer(
                data.manufacturerNo,
                GlobalTrader.StringHelper.setCleanTextValue(data.manufacturerName),
                data.mfrAdvisoryNotes
            ));
        }
        $col1.find("[name='dateCode']").text(data.dateCode);
        $col1.find("[name='product']").html(ProductHelper.showProductWarningIndividual(
            GlobalTrader.StringHelper.setCleanTextValue(data.productDescription),
            data.hazardousMsg,
            data.ipoMsg,
            data.restrictedMsg
        ));
        $col1.find("[name='dutyCodeRate']").text(data.dutyCodeAndRate);
        $col1.find("[name='package']").text(data.packageDescription);
        $col1.find("[name='partWatch']").attr('checked', data.partWatch);
        $col1.find("[name='factorySealed']").attr('checked', data.factorySealed);
        $col1.find("[name='msl']").text(data.msl);
        if (data.isNoBid) {
            $col1.find("[name='noBid']").closest('.row').show();
            $col1.find("[name='noBidNote']").closest('.row').show();
            $col1.find("[name='noBid']").text(localizeStrings.noBid);
            $col1.find("[name='noBidNote']").html(GlobalTrader.StringHelper.setCleanTextValue(data.noBidNotes, true));
        } else {
            $col1.find("[name='noBid']").closest('.row').hide();
            $col1.find("[name='noBidNote']").closest('.row').hide();
        }
        $col1.find("[name='countryOfOrigin']").text(data.countryOfOrigin);
        if (data.lifeCycleStage && data.lifeCycleStage.length > 0) {
            $col1.find("[name='partStatus']").html(IhsHelper.showIHSstatusDefi(
                data.lifeCycleStage,
                data.ihsStatusDefination
            ));
        } else {
            $col1.find("[name='partStatus']").text('');
        }
        $col1.find("[name='ihsProduct']").text(data.ihsProduct);
        $col1.find("[name='htsCode']").text(data.htsCode);
        if (data.eccnCode && data.eccnCode.length > 0) {
            $col1.find("[name='eccnCode']").html(IhsHelper.showIHSECCNCodeDefi(
                data.eccnCode,
                data.ihseccnCodeDefination
            ));
        } else {
            $col1.find("[name='eccnCode']").text('');
        }
        $col1.find("[name='packagingSize']").text(data.packagingSize);
        $col1.find("[name='descriptions']").text(data.descriptions);
    }

    bindColumn2(data) {
        if (!data) return;

        let lyticaUrl = '/lists/lytica-manufacturers';

        this.lyticaManufacturerRefSearchSelect = new PageSearchSelectComponent(
            'lyticaManufacturerRefInput',
            'lyticaManufacturerRefInputValue',
            'single',
            'search',
            lyticaUrl,
            0
        );

        this.lyticaManufacturerRefSearchSelect.debouncedLoadResults =
            this.lyticaManufacturerRefSearchSelect.debounce(
                (query) => this._loadLyticaManufacturers(query, data.part),
                500
            );

        this.lyticaManufacturerRefSearchSelect.showInactive = false;

        this.lyticaManufacturerRefSearchSelect.on('change', async (eventData) => {
            if (eventData && eventData.hiddenValue) {
                await this.onLyticaManufacturerChanged(eventData.hiddenValue, data.customerRequirementId);
            }
        });

        const $col2 = this._getPartDetailColumn2();
        $col2.find("[name='cusTargetPrice']").text(GlobalTrader.StringHelper.setCleanTextValue(data.priceFormated));
        $col2.find("[name='currency']").text(data.currencyDescription);
        $col2.find("[name='cusDateRequired']").text(data.datePromised);
        if (data.closed) {
            $col2.find("[name='closed']").text(window.localizedStrings.yes);
            $col2.find("[name='closed']").css('color', 'red');
        } else {
            $col2.find("[name='closed']").text(window.localizedStrings.no);
            $col2.find("[name='closed']").css('color', 'blue');
        }
        $col2.find("[name='reason1']").text(data.closedReason);
        $col2.find("[name='usage']").text(GlobalTrader.StringHelper.setCleanTextValue(data.usageName));
        $col2.find("[name='bom']").attr('checked', data.bom);
        $col2.find("[name='bomName']").text(data.bomName);
        $col2.find("[name='noteToCus']").html(GlobalTrader.StringHelper.setCleanTextValue(data.notes, true));
        $col2.find("[name='internalNote']").html(GlobalTrader.StringHelper.setCleanTextValue(data.instructions, true));
        $col2.find("[name='partialQuantityAcceptable']").attr('checked', data.partialQuantityAcceptable);

        $col2.find("[name='lyticaManufacturerRef']").closest('.row').show();
        $col2.find("[name='avgPrice']").closest('.row').show();
        $col2.find("[name='targetPrice']").closest('.row').show();
        $col2.find("[name='marketLeading']").closest('.row').show();

        if (data.lyticaManufacturerRef) {
            this.lyticaManufacturerRefSearchSelect.selectItem({
                value: data.lyticaManufacturerRef,
                label: data.lyticaManufacturerRef
            });
        }

        $col2.find("[name='avgPrice']").text(data.lyticaAveragePrice || '');
        $col2.find("[name='targetPrice']").text(data.lyticaTargetPrice || '');
        $col2.find("[name='marketLeading']").text(data.lyticaMarketLeading || '');

        const $col3 = this._getPartDetailColumn3();
        if (data.isPOHub) {
            $col3.find("[name='customerRefNo']").closest('.row').show();
        } else {
            $col3.find("[name='customerRefNo']").closest('.row').hide();
        }
    }

    bindColumn3(data) {
        if (!data) return;
        const $col3 = this._getPartDetailColumn3();
        $col3.find("[name='refurbsAcceptable']").attr('checked', data.refirbsAcceptable);
        $col3.find("[name='testingRequired']").attr('checked', data.testingRequired);
        $col3.find("[name='altAccepted']").attr('checked', data.alternativesAccepted);
        $col3.find("[name='bussiness']").attr('checked', data.repeatBusiness);
        $col3.find("[name='compBestOffer']").text(data.competitorBestOffer);
        $col3.find("[name='cusDecisionDate']").text(data.customerDecisionDate);
        $col3.find("[name='rfqClosingDate']").text(data.rfqClosingDate);
        $col3.find("[name='quoteValidityRequired']").text(data.quoteValidityText);
        $col3.find("[name='type']").text(data.reqTypeText);
        $col3.find("[name='ordToPlace']").attr('checked', data.orderToPlace);
        $col3.find("[name='reqForTrace']").text(data.reqForTraceabilityText);
        $col3.find("[name='estAnnualUsage']").text(data.eau);
        $col3.find("[name='customerRefNo']").text(data.customerRefNo);
    }

    _getPartDetailColumn1() {
        return $(`#hubrfq-item-detail #column-1`);
    }
    _getPartDetailColumn2() {
        return $(`#hubrfq-item-detail #column-2`);
    }
    _getPartDetailColumn3() {
        return $(`#hubrfq-item-detail #column-3`);
    }

    setDetailLoading(isLoading) {
        if (isLoading) {
            $("#hubrfq-item-detail-loading-indicator").html(
                `<div class="d-flex">
                    <div class="spinner-loader me-1"></div>
                    <div class="text-loader"></div>
                </div>`);
            $("#hubrfq-item-detail").addClass('d-none');
        }
        else {
            $("#hubrfq-item-detail-loading-indicator").html("");
            $("#hubrfq-item-detail").removeClass('d-none');
        }
    }

    _writeRohs(rohs) {
        const match = ROHS_STATUSES.find(entry => entry.status === rohs);
        const tooltip = match ? match.tooltip : "";
        return ROHSHelper.writePartNo(tooltip, rohs);
    }

    _clearLyticaPricingFields() {
        const $col2 = this._getPartDetailColumn2();
        $col2.find("[name='avgPrice']").text('');
        $col2.find("[name='targetPrice']").text('');
        $col2.find("[name='marketLeading']").text('');
    }

    _cleanupSearchSelect() {
        if (this.lyticaManufacturerRefSearchSelect) {
            try {
                this.lyticaManufacturerRefSearchSelect.resetSearchSelect(false);
            } catch (e) {
                console.warn('Error during component reset:', e);
            }
            this.lyticaManufacturerRefSearchSelect = null;
        }
        this._clearLyticaPricingFields();
    }

    async _loadLyticaManufacturers(query, partNo) {

        try {
            if (!this.lyticaManufacturerRefSearchSelect.isSearchInputFocused) {
                return;
            }

            const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };

            const requestBody = {
                search: query || '',
                lyticaPartNo: partNo || null,
                showInactive: false
            };

            const response = await GlobalTrader.ApiClient.postAsync('/lists/lytica-manufacturers', requestBody, header);

            const data = response.data;

            const transformedData = data.map(item => ({
                label: item.name,
                value: item.name,
                name: item.name
            }));


            if (this.lyticaManufacturerRefSearchSelect) {
                this.lyticaManufacturerRefSearchSelect.showMessage(
                    this.lyticaManufacturerRefSearchSelect.getResultFoundMessage(transformedData.length)
                );
                this.lyticaManufacturerRefSearchSelect.renderResults(transformedData);
            }
        } catch (error) {
            this.lyticaManufacturerRefSearchSelect.showMessage('Error loading manufacturers');
        }
    }

    async onLyticaManufacturerChanged(manufacturerName, customerRequirementId) {
        try {
            const response = await GlobalTrader.ApiClient.getAsync('/orders/bom/lytica-manufacturer', {
                rsManufacturerName: manufacturerName,
                customerRequirementId: customerRequirementId
            });

            if (response.success && response.data) {
                const lyticaData = response.data;
                const $col2 = this._getPartDetailColumn2();

                $col2.find("[name='avgPrice']").text(lyticaData.averagePrice || '');
                $col2.find("[name='targetPrice']").text(lyticaData.targetPrice || '');
                $col2.find("[name='marketLeading']").text(lyticaData.marketLeading || '');
            } else {
                this._clearLyticaPricingFields();
            }
        } catch (error) {
            console.error('Error fetching Lytica manufacturer data:', error);
            this._clearLyticaPricingFields();
        }
    }

    _showStockDetails(data, $col1) {
        const stocks = data.stockAvailableDetail.split('-');
        $col1.find("[name='stockAlert']").show();
        $col1.find("[name='stockAlert'] [name='inStock']").text(stocks[0]).attr('title', stocks[0]);
        $col1.find("[name='stockAlert'] [name='onOrder']").text(stocks[1]).attr('title', stocks[1]);
        $col1.find("[name='stockAlert'] [name='allocated']").text(stocks[2]).attr('title', stocks[2]);
        $col1.find("[name='stockAlert'] [name='available']").text(stocks[3]).attr('title', stocks[3]);
    }
}