﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;
using Microsoft.IdentityModel.Tokens;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportShipped
{
    public class GetSalesOrderLineReportShippedHandler : IRequestHandler<GetSalesOrderLineReportShippedQuery, BaseResponse<IReadOnlyList<SalesOrderLineReportShippedDto>>>
    {
        private readonly IBaseRepository<SalesOrderLineReportShippedReadModel> _salesOrderLinePoRepository;
        private readonly IMapper _mapper;
        public GetSalesOrderLineReportShippedHandler(IBaseRepository<SalesOrderLineReportShippedReadModel> salesOrderLinePoRepository, IMapper mapper)
        {
            _salesOrderLinePoRepository = salesOrderLinePoRepository;
            _mapper = mapper;
        }
        public async Task<BaseResponse<IReadOnlyList<SalesOrderLineReportShippedDto>>> Handle(GetSalesOrderLineReportShippedQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IReadOnlyList<SalesOrderLineReportShippedDto>>();

            var paramters = new List<SqlParameter>()
            { new SqlParameter("@SalesOrderLineId", SqlDbType.Int) { Value = request.SalesOrderLineId } };
            var queryResult = await _salesOrderLinePoRepository.SqlQueryRawAsync
                ($"{StoredProcedures.Get_All_SalesOrderLine_ReportShipped} @SalesOrderLineId", paramters.ToArray());
            if (queryResult.Any())
            {
                response.Data = _mapper.Map<IReadOnlyList<SalesOrderLineReportShippedDto>>(queryResult);
            }
            response.Success = true;
            return response;
        }
    }
}
