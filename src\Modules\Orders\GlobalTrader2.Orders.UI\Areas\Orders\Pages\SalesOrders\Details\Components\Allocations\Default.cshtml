﻿@using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.CollapsibleFieldset
@using GlobalTrader2.SharedUI.Enums
@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.SharedUI.Services
@inject IWebResourceManager WebResourceManager
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer _localizer
@inject SettingManager _settingManager

<div id="allocations-container" class="fieldset-content-wrapper">
    <span class="d-flex">
      @*   @if (_sessionManager.IsPOHub && Model.SourcingSectionBoxModel.CanAddRequirement && Model.SourcingSectionBoxModel.PageType == SourcingPageType.HUBRFQDetails)
        {
            <button type="button" class="btn btn-primary" id="stock-on-order-add-to-requirement" disabled>
                <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                <span class="lh-base">@_localizer["Add To Requirement"]</span>
            </button>
            <span id="stock-on-order-missing-supplier" class="d-none" title="@_localizer["Missing Supplier"]">
                <img alt="system manufacturer info" src="/img/icons/circle-info-yellow.svg" class="ms-1 rounded-circle bg-white" height="14">
            </span>
        } *@
    </span>
    <div id="allocations-table-wrapper" class="mt-2">
        <table id="allocations-table" class="table simple-table display responsive">
            <thead>
                <tr>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
