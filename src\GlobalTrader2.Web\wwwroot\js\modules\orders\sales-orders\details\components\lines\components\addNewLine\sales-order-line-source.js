﻿import { SourceFilterComponent } from './source-filter-component.js?v=#{BuildVersion}#';
import { SourceItemModel } from '../../models/source-item.model.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';
import { FieldType } from '../../../../../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { NumberType } from "../../../../../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#";
import { TextFilterHelper } from "../../../../../../../../helper/text-filter-helper.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#";
export class SalesOrderLineSourceManager {
    constructor({ onSelectedSourceItem = (data) => { } }) {
        this.tableFilter = null;
        this.salesOrderLineSourceTable = null;
        this.salesOrderLineSourceUrl = "/api/orders/sales-order/so-lines/item-search";
        this.onSelectedSourceItem = onSelectedSourceItem;
        this.pageSize = $(`#add-lines-dialog`).data('default-page-size') || 10;
        this.filterInputs = [
            {
                fieldType: FieldType.NUMBER,
                label: 'Sales Order',
                name: 'SoNo',
                id: 'SoNo',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true,
                },
                extraPros: {
                    numberType: NumberType.INT
                },
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Part No',
                name: 'PartNo',
                id: 'PartNo',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Company',
                name: 'Company',
                id: 'Company',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'Salesperson',
                name: 'Salesman',
                id: 'Salesman',
                value: '',
                options: {
                    serverside: false,
                    endpoint: "setup/security-settings/security-users/users-client",
                    valueKey: 'loginId',
                    textKey: 'employeeName',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: 'Include Closed?',
                name: 'IncludeClosed',
                id: 'IncludeClosed',
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: 'Only From IPO',
                name: 'OnlyFromIPO',
                id: 'OnlyFromIPO',
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Customer PO',
                name: 'CustomerPO',
                id: 'CustomerPO',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Ordered From',
                name: 'DateOrderedFrom',
                id: 'DateOrderedFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-2',
                pairWith: "DateOrderedTo"
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Ordered To',
                name: 'DateOrderedTo',
                id: 'DateOrderedTo',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Promised From',
                name: 'DatePromisedFrom',
                id: 'DatePromisedFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-2',
                pairWith: "DatePromisedTo"
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Promised To',
                name: 'DatePromisedTo',
                id: 'DatePromisedTo',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
        ]
    }

    async initialize() {
        $("#sales-order-line-source").show();
        await this.initTableFilter();
        this.setDefaultFilter();
        this.initDataTable();
    }

    setDefaultFilter() {
        if (SODetailGeneralInfo.isFromIPO) {
            const soNoInput = this.tableFilter.getInputElementByName("SoNo");
            soNoInput.setRequiredCheckbox(true);
            soNoInput.setValue(SODetailGeneralInfo.salesOrderNo);
            soNoInput.syncInputState();
            soNoInput.triggerControlChanged();
        }
        else {
            const companyInput = this.tableFilter.getInputElementByName("Company");
            companyInput.setRequiredCheckbox(true);
            companyInput.setValue(TextFilterHelper.getFilterValue(companyInput.textFilterComponent.currentFilterIndex, SODetailGeneralInfo.companyName));
            companyInput.syncInputState();
            companyInput.triggerControlChanged();

            const datePromisedFromInput = this.tableFilter.getInputElementByName("DatePromisedFrom");
            datePromisedFromInput.setRequiredCheckbox(true);
            datePromisedFromInput.setValue(GlobalTrader.DatetimeHelper.oneWeekAgo());
            datePromisedFromInput.syncInputState();
            datePromisedFromInput.triggerControlChanged();
        }
    }

    async initTableFilter() {
        this.tableFilter = new SourceFilterComponent('#sales-order-line-source-filter-section-wrapper', 'Search for and select the item you would like to use as the source for the new Line and press Continue', {
            inputConfigs: this.filterInputs,
            wrapperClass: 'bg-none m-0 p-0'
        });

        await this.tableFilter.init();
        this.tableFilter.on('applied.mtf', () => {
            this.salesOrderLineSourceTable.ajax.url(this.salesOrderLineSourceUrl);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.salesOrderLineSourceTable, true);
        })
        this.tableFilter.on('cancel.mtf', () => {
            if (window.currentXhr) {
                window.currentXhr.abort();
                if ($('#salesOrderLineSourceTbl_processing').is(':visible')) {
                    $('#salesOrderLineSourceTbl_processing').hide();
                }
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        })
    }

    initDataTable() {
        this.salesOrderLineSourceTable = new DataTable('#salesOrderLineSourceTbl', {
            scrollCollapse: true,
            paging: true,
            dataSrc: 'data',
            serverSide: true,
            lengthMenu: [5, 10, 25, 50],
            pageLength: this.pageSize,
            ajax: {
                url: this.tableFilter.hasAnyActiveFilter() ? this.salesOrderLineSourceUrl : "",
                type: 'POST',
                contentType: 'application/json',
                beforeSend: (xhr) => {
                    window.currentXhr = xhr;
                },
                data: (data) => {
                    const filterValues = this.tableFilter.getAllValue();
                    return JSON.stringify({
                        soNoLo: filterValues.SoNo.isOn ? filterValues.SoNo.low : null,
                        soNoHi: filterValues.SoNo.isOn ? filterValues.SoNo.hi : null,
                        includeClosed: filterValues.IncludeClosed.isOn ? filterValues.IncludeClosed.value : false,
                        onlyFromIPO: filterValues.OnlyFromIPO.isOn ? filterValues.OnlyFromIPO.value : false,
                        partSearch: filterValues.PartNo.isOn ? filterValues.PartNo.value : null,
                        companySearch: filterValues.Company.isOn ? filterValues.Company.value : null,
                        customerPO: filterValues.CustomerPO.isOn ? filterValues.CustomerPO.value : null,
                        dateOrderedFrom: filterValues.DateOrderedFrom.isOn ? this.formatDateFilter(filterValues.DateOrderedFrom.value) : null,
                        dateOrderedTo: filterValues.DateOrderedTo.isOn ? this.formatDateFilter(filterValues.DateOrderedTo.value) : null,
                        datePromisedFrom: filterValues.DatePromisedFrom.isOn ? this.formatDateFilter(filterValues.DatePromisedFrom.value) : null,
                        datePromisedTo: filterValues.DatePromisedTo.isOn ? this.formatDateFilter(filterValues.DatePromisedTo.value) : null,
                        draw: data.draw,
                        index: data.start,
                        size: data.length,
                        sortDir: data.order[0]?.column ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : 1,
                        orderBy: data.order[0]?.column ? data.order[0].column : 1,
                    });
                },
            },
            info: true,
            responsive: true,
            select: {
                style: 'single'
            },
            ordering: true,
            searching: false,
            processing: true,
            columnDefs: [
                { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
            ],
            language: {
                emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                infoFiltered: "",
                lengthMenu: "_MENU_ per page",
                loadingRecords: "",
            },
            dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                '<"dt-layout-cell dt-layout-end" p >><"clear">',
            rowId: "salesOrderLineId",
            order: [[0, "desc"]],
            columns: [
                {
                    data: "salesOrderNumber",
                    title: "SO",
                    type: 'string',
                },
                {
                    data: "companyName",
                    title: "Company",
                    type: 'string',
                },
                {
                    data: null,
                    title: "Part No",
                    render: (row) => `${ROHSHelper.writePartNo(row.part, row.rohs)}`
                },
                {
                    data: "dateOrderedText",
                    title: "Ordered",
                    type: 'string',
                },
                {
                    data: "formatedPrice",
                    title: "Unit Price",
                    type: 'string',
                },
                {
                    data: "salesmanName",
                    title: "Salesperson",
                    type: 'string',
                },
                {
                    data: "customerPO",
                    title: "Customer PO",
                    type: 'string',
                },
            ]
        }).on('draw.dt', () => {
            this.salesOrderLineSourceTable.columns.adjust();
        }).on('processing.dt', (e, settings, processing) => {
            this.removeNeutralSortingIcon(this.salesOrderLineSourceTable);
            if (processing) {
                // Table is processing (e.g., AJAX call happening)
                this.tableFilter.toggleApplyCancelButtons(false);
            } else {
                // Done processing
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        });

        this.salesOrderLineSourceTable.on('select', async (e, dt, type, indexes) => {
            if (type === 'row') {
                const selectedRowId = dt.row(indexes).id();
                this.onSelectedSourceItem(new SourceItemModel(FromSourceTypeConstant.SO, selectedRowId));
            };
        })
    }

    clearTable() {
        $("#sales-order-line-source").hide();
        if ($('#salesOrderLineSourceTbl_processing').is(':visible')) {
            $('#salesOrderLineSourceTbl_processing').hide();
        }
        if (this.salesOrderLineSourceTable != null) {
            this.salesOrderLineSourceTable.rows('.selected').deselect();
            this.salesOrderLineSourceTable.ajax.url('').load();
            this.salesOrderLineSourceTable.ajax.url(this.salesOrderLineSourceUrl);
        }
    }

    doSearch() {
        $("#sales-order-line-source").show();
        if (this.tableFilter.hasAnyActiveFilter()) {
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.salesOrderLineSourceTable, true);
        }
    }

    formatDateFilter(dateString) {
        const [day, month, year] = dateString.split("/");
        return `${year}-${month}-${day}T00:00:00.000Z`
    }

    removeNeutralSortingIcon(datatable) {
        // Remove neutral sorting icon
        const tableId = datatable.table().node().id;
        $(`#${tableId} thead th`)
            .removeClass('dt-orderable-asc dt-orderable-desc')
            .addClass('position-relative');

        $(`#${tableId} thead th:not(.dt-orderable-none)`)
            .attr('role', 'button');

        $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
    }
}