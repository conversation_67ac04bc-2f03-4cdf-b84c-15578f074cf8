﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Ganss.Xss;
using GlobalTrader2.Aggregator.UseCases.Common.NotificationAndSendEmail.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetSaleOrderMainInfo;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.NotifyApproveRejectExportApproval;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.PayByCreditCard;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.SendApprovalRequest;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.UpdateAuthorise;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.File;
using GlobalTrader2.Dto.MailMessages;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.ApproveRejectExportApproval;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.CloseSalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.ReadyToShip;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.RequestApprovalExportApproval;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.SentSalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.UpdateAllExportApprovalDetails;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.UpdateExportApprovalDetails;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.UpdateSalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.Authorisation;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetAddVancePaymentNotification;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetAuthorisationInfo;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetEditExportApproval;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetExportApprovalDataById;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetExportApprovalLineByTab;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetNotifySOMessageTemplate;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetOGELSelectSONotifier;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetOpenSalesOrderForCompany;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSaleOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.SONotifyer;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.CountAllSoLinesBySoId;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetAllSoLinesforEuu;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSoLineListBySoId;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.SharedUI.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/orders/sales-order-detail")]
    public class SalesOrderDetailController : ApiBaseController
    {
        private readonly IMediator _mediator;
        private readonly IStringLocalizer<Misc> _miscLocalizer;
        private readonly IStringLocalizer<MessageResources> _messageLocalizer;
        private readonly IPowerAutomateApiClient _powerAutomateApiClient;
        private readonly SessionManager _sessionManager;
        private readonly IStringLocalizer<Printing> _printingLocalizer;
        private readonly SecurityManager _securityManager;
        private readonly IConfiguration _configuration;
        private readonly HtmlSanitizer _sanitizer = new HtmlSanitizer();
        private readonly IWebHostEnvironment _hostingEnvironment;
        public SalesOrderDetailController(IMediator mediator
            , IStringLocalizer<Misc> miscLocalizer
            , IStringLocalizer<MessageResources> messageLocalizer
            , IPowerAutomateApiClient powerAutomateApiClient
            , SessionManager sessionManager
            , IStringLocalizer<Printing> printingLocalizer
            , SecurityManager securityManager
            , IConfiguration configuration
            , IWebHostEnvironment hostingEnvironment)
        {
            _mediator = mediator;
            _miscLocalizer = miscLocalizer;
            _messageLocalizer = messageLocalizer;
            _powerAutomateApiClient = powerAutomateApiClient;
            _sessionManager = sessionManager;
            _printingLocalizer = printingLocalizer;
            _securityManager = securityManager;
            _configuration = configuration;
            _hostingEnvironment = hostingEnvironment;
        }
        #region Authorisation
        [HttpGet("{salesOrderId}/authorisation-info")]
        public async Task<IActionResult> GetAuthorisationInfo(int salesOrderId, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(new GetSalesOrderDetailForAuthorisationQuery()
            {
                SalesOrderId = salesOrderId,
                ClientId = ClientId,
                CultureInfo = new CultureInfo(Culture),
                LoginId = UserId,

            }, cancellationToken);
            if (result.Data != null)
            {
                result.Data.FormattedDLUP = result.Data.FormattedDLUP = LocalizerHelper.FormatDLUP(result.Data.DLUP, result.Data.UpdatedByName, _miscLocalizer, CultureInfo.CurrentCulture);
                result.Data.RequestSubject = _messageLocalizer["SORRequestSubject"] != null
                    ? _messageLocalizer["SORRequestSubject"].ToString().Replace("~SONO", result.Data.SalesOrderNumber.ToString())
                    : null;
            }
            return Ok(result);
        }
        [HttpGet("{salesOrderId}/authorisation-history")]
        public async Task<IActionResult> GetAuthorisationHistory(int salesOrderId, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(new GetAuthorisationHistoryQuery()
            {
                SalesOrderId = salesOrderId,
                CultureInfo = new CultureInfo(Culture)
            }, cancellationToken);

            result?.Data?.Select(z =>
           {
               z.Action = _miscLocalizer[$"{z.Note ?? string.Empty}"] ?? string.Empty;
               return z;
           }).ToList();

            return Ok(result);
        }
        [HttpGet("{companyId}/open-sales-order")]
        public async Task<IActionResult> GetOpenSalesOrderForCompany(int companyId, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(new GetOpenSalesOrderForCompanyQuery()
            {
                CompanyId = companyId,
                CultureInfo = new CultureInfo(Culture)
            }, cancellationToken);
            return Ok(result);
        }
        [HttpGet("{salesOrderId}/so-notifyer")]
        public async Task<IActionResult> GetSONotifyer(int salesOrderId, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(new GetSONotifyerQuery()
            {
                SalesOrderId = salesOrderId,
                LoginId = UserId
            }, cancellationToken);

            return Ok(result);
        }

        [HttpPost("{salesOrderId}/approval-request")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_SalesOrder_Authorise_RequestApproval)]
        public async Task<IActionResult> SendApprovalRequest(int salesOrderId, [FromBody] SendApprovalRequest request)
        {
            string baseDirectory = Path.Combine(_hostingEnvironment.WebRootPath, "img","icons");
            var v1redirectBaseUrl = _configuration[AppSettingKeys.GTv1SiteUrl];
            var v2redirectBaseUrl = RequestHelper.GetApplicationUrl(HttpContext.Request);
            var result = await _mediator.Send(new SendApprovalRequestCommand
            {
                LoginId = UserId,
                ClientId = ClientId,
                SalesOrderId = salesOrderId,
                Subject = request.Subject,
                IsNotifySO = request.IsNotifySO,
                ApproverIds = request.ApproverIds,
                Message = request.Message,
                Uri = v2redirectBaseUrl,
                V1Uri = v1redirectBaseUrl ?? string.Empty,
                LoginFullName = LoginFullName,
                BaseDirectory = baseDirectory
            });
            return Ok(result);

        }
        [HttpPost("{salesOrderId}/allow-ready-to-ship")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_SalesOrder_SOAuth_AllowReadyToShip)]
        public async Task<IActionResult> AllowReadyToShip(int salesOrderId)
        {
            var result = await _mediator.Send(new AllowReadyToShipCommand()
            {
                SalesOrderId = salesOrderId,
                LoginId = UserId
            });
            return Ok(result);
        }
        [HttpPost("{salesOrderId}/authorise-deauthorise")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_SalesOrder_MainInfo_Authorise)]
        public async Task<IActionResult> UpdateAuthorise(int salesOrderId, [FromBody] UpdateAuthoriseRequest request)
        {
            string baseDirectory = Path.Combine(_hostingEnvironment.WebRootPath, "img", "icons");

            var result = await _mediator.Send(new UpdateAuthoriseCommand()
            {
                SalesOrderId = salesOrderId,
                LoginId = UserId,
                IsAllowReadyToShip = request.IsAllowReadyToShip,
                IsAuthorise = request.IsAuthorise,
                Comment = request.Comment,
                IsNotify = request.IsNotify,
                CreateIPOSubject = _messageLocalizer["CreateIPOSubject"] ?? string.Empty,
                SOAuthoriseSubject = _messageLocalizer["SOAuthorise"] ?? string.Empty,
                LoginEmail = LoginEmail,
                LoginName = LoginFullName,
                HostUrl = RequestHelper.GetApplicationUrl(HttpContext.Request),
                PurchaseOrderUrl = RedirectUrlHelper.GetUrlWithoutParams_PurchaseOrder(),
                InternalPurchaseOrderUrl = RedirectUrlHelper.GetUrlWithoutParams_InternalPurchaseOrder(),
                BaseDirectory = baseDirectory,
                ClientId = ClientId

            });
            return Ok(result);
        }
        #endregion

        [HttpGet("{salesOrderId}/customer-po-pdf/authorize-by")]
        public async Task<IActionResult> GetAuthorizeByCustomerPoOnlyPdfAsync(int salesOrderId)
        {
            return Ok(await _mediator.Send(new GetSaleOrderQuery(salesOrderId, 0, 0)));
        }

        [HttpGet("{salesOrderId}/all-so-lines")]
        public async Task<IActionResult> GetAllSOLinesBySalesOrderIdAsync(int salesOrderId)
        {
            var soLineResponse = await _mediator.Send(new GetSoLineListBySoIdQuery(salesOrderId, ClientId, GetSoLineType.All));
            return Ok(soLineResponse);
        }

        [HttpGet("{salesOrderId}/open-so-lines")]
        public async Task<IActionResult> GetOpenSOLinesBySalesOrderIdAsync(int salesOrderId)
        {
            var soLineResponse = await _mediator.Send(new GetSoLineListBySoIdQuery(salesOrderId, ClientId, GetSoLineType.Open));
            return Ok(soLineResponse);
        }
        [HttpGet("{salesOrderId}/closed-so-lines")]
        public async Task<IActionResult> GetClosedSOLinesBySalesOrderIdAsync(int salesOrderId)
        {
            var soLineResponse = await _mediator.Send(new GetSoLineListBySoIdQuery(salesOrderId, ClientId, GetSoLineType.Closed));
            return Ok(soLineResponse);
        }

        [HttpPut("{salesOrderId}/close")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CloseSalesOrderAsync(int salesOrderId, [FromBody] CloseSalesOrderRequest request)
        {
            await CheckUserPermission(request.CompanyId, SecurityFunction.Orders_SalesOrder_MainInfo_Close, isDataOtherClient: false);
            var closeCommand = new CloseSalesOrderCommand()
            {
                ResetQuantity = request.ResetQuantity,
                SalesOrderId = salesOrderId,
                UpdatedBy = UserId
            };

            var result = await _mediator.Send(closeCommand);
            return Ok(result);
        }

        [HttpPut("{salesOrderId}/confirm-so-order")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendSalesOrder(int salesOrderId)
        {
            var sendOrderCommand = new SentSalesOrderCommand
            {
                SalesOrderId = salesOrderId,
                UpdatedBy = UserId
            };
            var result = await _mediator.Send(sendOrderCommand);
            return Ok(result);
        }

        [HttpGet("{salesOrderId}/all-export-approval-so-lines")]
        public async Task<IActionResult> GetAllExportApprovalSOLines(int salesOrderId)
        {
            var exportApprovalSOLines = await _mediator.Send(new GetExportApprovalLineByTabQuery(salesOrderId, ClientId, UserId, (int)ExportApprovalStatusTab.All, _sessionManager.GetCurrentCulture()));
            return Ok(exportApprovalSOLines);
        }

        [HttpGet("{salesOrderId}/approved-export-approval-so-lines")]
        public async Task<IActionResult> GetApprovedExportApprovalSOLines(int salesOrderId)
        {
            var exportApprovalSOLines = await _mediator.Send(new GetExportApprovalLineByTabQuery(salesOrderId, ClientId, UserId, (int)ExportApprovalStatusTab.Approved, _sessionManager.GetCurrentCulture()));
            return Ok(exportApprovalSOLines);
        }

        [HttpGet("{salesOrderId}/waiting-export-approval-so-lines")]
        public async Task<IActionResult> GetWaitingExportApprovalSOLines(int salesOrderId)
        {
            var exportApprovalSOLines = await _mediator.Send(new GetExportApprovalLineByTabQuery(salesOrderId, ClientId, UserId, (int)ExportApprovalStatusTab.Waiting, _sessionManager.GetCurrentCulture()));
            return Ok(exportApprovalSOLines);
        }

        [HttpGet("{salesOrderId}/main-info")]
        public async Task<IActionResult> GetSaleOrderMainInfoAsync(int salesOrderId)
        {
            await _securityManager.LoadUserPagePermissions(UserId, SiteSection.Orders, SitePage.Orders_SalesOrderDetail, false);
            bool canEditOgel = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_EditOGEL);
            var response = await _mediator.Send(new GetSaleOrderMainInfoQuery()
            {
                SaleOrderId = salesOrderId,
                ClientCurrencyCode = _sessionManager.ClientCurrencyCode,
                ClientCurrencyID = _sessionManager.ClientCurrencyID.GetValueOrDefault(),
                ClientNo = ClientId,
                LoginId = UserId
            });

            if (response.Data != null)
            {
                response.Data.UpdatedByText = LocalizerHelper.FormatDLUP(response.Data.DLUP, response.Data.UpdatedByText ?? "", _miscLocalizer, CultureInfo.CurrentCulture);
                response.Data.PurchasingNotes = _sanitizer.Sanitize(response.Data.PurchasingNotes ?? "");
                response.Data.InternalNotes = _sanitizer.Sanitize(response.Data.InternalNotes ?? "");
                response.Data.CustomerNotes = _sanitizer.Sanitize(response.Data.CustomerNotes ?? "");

                var isSoHasAnyLineResult = await _mediator.Send(new CountAllSoLinesBySoIdQuery()
                {
                    SalesOrderId = salesOrderId
                });
                if (isSoHasAnyLineResult.Success)
                {
                    response.Data.IsSoHasAnyLine = isSoHasAnyLineResult.Data;
                }
                response.Data.IsOGELDisabled = !canEditOgel || response.Data.IsOGELDisabled == true;
            }
            return Ok(response);
        }

        [HttpGet("export-approval/{exportApprovalId}/edit-data")]
        public async Task<IActionResult> GetExportApprovalEditData(int exportApprovalId)
        {
            var exportApproval = await _mediator.Send(new GetEditExportApprovalQuery(exportApprovalId));
            return Ok(exportApproval);
        }

        [HttpPut("export-approval/{exportApprovalId}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateExportApprovalDetails([FromRoute] int exportApprovalId, [FromBody] EditExportApprovalRequest request)
        {
            await CheckUserPermission(salesOrderClientId: request.SalesOrderClientId, pagePermission: SecurityFunction.Orders_SalesOrder_ExportApproval_EditApproval);
            var command = new UpdateExportApprovalDetailsCommand()
            {
                LoginId = UserId,
                ExportApprovalId = exportApprovalId,
                AerospaceUse = request.AerospaceUse,
                DestinationCountryNo = request.DestinationCountryId,
                EndUserText = request.EndUserText,
                ExportControl = request.ExportControl,
                MilitaryUseNo = request.MilitaryUseId,
                PartApplication = request.PartApplication,
                PartTested = request.PartTested
            };
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpPut("export-approval/all")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateAllExportApprovalDetails([FromBody] EditAllExportApprovalRequest request)
        {
            await CheckUserPermission(salesOrderClientId: request.SalesOrderClientId, pagePermission: SecurityFunction.Orders_SalesOrder_ExportApproval_EditApproval);
            string ids = (request.ExportApprovalIds.Count > 0) ? string.Join("||", request.ExportApprovalIds) : string.Empty;
            var command = new UpdateAllExportApprovalCommand()
            {
                LoginId = UserId,
                ExportApprovalIds = ids,
                DestinationCountryNo = request.DestinationCountryId,
                EndUserText = request.EndUserText,
                MilitaryUseNo = request.MilitaryUseId
            };
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpGet("export-approval/{exportApprovalId}")]
        public async Task<IActionResult> GetExportApprovalById(int exportApprovalId)
        {
            var exportApproval = await _mediator.Send(new GetExportApprovalDataByIdQuery()
            {
                ExportApprovalId = exportApprovalId
            });
            if (exportApproval.Success && exportApproval.Data != null)
            {
                exportApproval.Data.Subject = string.Format(_messageLocalizer["SendExportApproval"], exportApproval.Data.SalesOrderNumber);
            }
            return Ok(exportApproval);
        }

        [HttpPut("export-approval/{exportApprovalId}/approve")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ApproveExportApprovalById([FromRoute] int exportApprovalId, [FromBody] ApproveRejectExportApprovalRequest request)
        {
            await CheckUserPermission(salesOrderClientId: request.SalesOrderClientId, pagePermission: SecurityFunction.Orders_SalesOrder_ExportApproval_SendApproval);
            var exportApproval = await _mediator.Send(new ApproveRejectExportApprovalCommand()
            {
                ExportApprovalId = exportApprovalId,
                ActionOption = (int)ExportApprovalActionOption.APPROVE,
                Comment = request.Comment,
                EndUser = request.EndUser,
                MilitaryUse = request.MilitaryUse,
                LoginId = UserId,
                OgelNumber = request.OgelNumber
            });
            return Ok(exportApproval);
        }

        [HttpPut("export-approval/{exportApprovalId}/reject")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectExportApprovalById([FromRoute] int exportApprovalId, [FromBody] ApproveRejectExportApprovalRequest request)
        {
            await CheckUserPermission(salesOrderClientId: request.SalesOrderClientId, pagePermission: SecurityFunction.Orders_SalesOrder_ExportApproval_SendApproval);
            var exportApproval = await _mediator.Send(new ApproveRejectExportApprovalCommand()
            {
                ExportApprovalId = exportApprovalId,
                ActionOption = (int)ExportApprovalActionOption.REJECT,
                Comment = request.Comment,
                EndUser = request.EndUser,
                MilitaryUse = request.MilitaryUse,
                LoginId = UserId,
                OgelNumber = request.OgelNumber
            });
            return Ok(exportApproval);
        }

        [HttpGet("export-approval/{exportApprovalId}/so-notifier")]
        public async Task<IActionResult> GetOGELSelectSONotifier(int exportApprovalId)
        {
            var ogelSelectSONotifier = await _mediator.Send(new GetOgelSelectSoNotifierQuery()
            {
                ExportApprovalId = exportApprovalId,
                LoginId = UserId
            });
            return Ok(ogelSelectSONotifier);
        }

        [HttpPost("{salesOrderId}/approve-export-approval-notify")]
        public async Task<IActionResult> NotifyExportApprovalApproved([FromRoute] int? salesOrderId, [FromBody] NotifyExportApprovalRequest request) => await NotifyExportApprovalAsync(salesOrderId, request, true);

        [HttpPost("{salesOrderId}/reject-export-approval-notify")]
        public async Task<IActionResult> NotifyExportApprovalRejected([FromRoute] int? salesOrderId, [FromBody] NotifyExportApprovalRequest request) => await NotifyExportApprovalAsync(salesOrderId, request, false);

        private async Task<IActionResult> NotifyExportApprovalAsync(int? salesOrderId, NotifyExportApprovalRequest request, bool isApproved)
        {
            var localizationResource = new Dictionary<string, string>{
                { "OGELApprovalStatus", _messageLocalizer["OGEL Approval Status"] },
                { "EmailTo", _printingLocalizer["Email To"] }
            };

            var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
            var urlEndpoint = (salesOrderId.HasValue) ? RedirectUrlHelper.GetUrlWithParams_SalesOrder(salesOrderId.Value) : string.Empty;
            var senderName = _sessionManager.LoginFullName;
            var command = new NotifyApproveRejectExportApprovalCommand()
            {
                LoginId = UserId,
                FromEmail = _sessionManager.GetString(SessionKey.LoginEmail),
                Resources = localizationResource,
                Comment = request.Comment,
                ExportApprovalStatus = request.ExportApprovalStatus,
                IsApproved = isApproved,
                Salesman = request.Salesman,
                SerialNo = request.SerialNo,
                SalesmanName = request.SalesmanName,
                SalesOrderNo = request.SerialNo,
                SalesOrderNumber = request.SalesOrderNumber,
                RedirectUrl = $"{urlBase}{urlEndpoint}",
                SenderName = senderName,
                CompanyNo = request.CompanyNo
            };
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        private async Task CheckUserPermission(int salesOrderClientId, SecurityFunction pagePermission, bool isDataOtherClient = false)
        {
            await _securityManager.LoadUserPagePermissions(UserId, SiteSection.Orders, SitePage.Orders_SalesOrderDetail, isDataOtherClient);
            bool canAction = _securityManager.CheckPagePermission(pagePermission);


            if (salesOrderClientId != ClientId && _sessionManager.IsGSA && !_sessionManager.IsGlobalUser)
            {
                canAction = false;
            }

            if (!canAction)
            {
                throw new ValidationException($"Has no permission: {pagePermission}");
            }
        }

        [HttpPut("{salesOrderId}/main-info")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_SalesOrder_MainInfo_Edit)]
        public async Task<IActionResult> UpdateSalesOrderMainInfo([FromRoute] int salesOrderId, [FromBody] UpdateSalesOrderCommandRequest request)
        {
            var command = new UpdateSalesOrderCommand
            {
                ClientId = ClientId,
                LoginId = UserId,
                Data = request,
                SalesOrderId = salesOrderId
            };

            var updateSOInfoResult = await _mediator.Send(command);
            if (updateSOInfoResult.Success && request.IsPaid)
            {
                var urlPath = Navigations.SalesOrderDetail(string.Empty).CtaUri;
                var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
                await SOAddvancePaymentNotification(salesOrderId, $"{urlBase}{urlPath}");
            }
            return Ok(updateSOInfoResult);
        }

        private async Task SOAddvancePaymentNotification(int salesOrderId, string salesOrderDetailBaseUrl)
        {
            var getAddVancePaymentNotification = await _mediator.Send(new GetAddVancePaymentNotificationQuery
            {
                SalesOrderId = salesOrderId
            });

            if (getAddVancePaymentNotification.Success && getAddVancePaymentNotification.Data != null)
            {
                var strcompany = getAddVancePaymentNotification.Data.CompanyName?.Replace("\"", "\\\"") ?? string.Empty;
                await _powerAutomateApiClient.SendSOAddvancePaymentNotificationAsync(
                    getAddVancePaymentNotification.Data.PowerSorUrl ?? string.Empty,
                    "SO Advance Payment Notification",
                    getAddVancePaymentNotification.Data.ToEmail ?? string.Empty,
                    getAddVancePaymentNotification.Data.SalesmanName ?? string.Empty,
                    getAddVancePaymentNotification.Data.SalesOrderId,
                    getAddVancePaymentNotification.Data.SalesOrderNumber,
                    getAddVancePaymentNotification.Data.ContactName ?? string.Empty,
                    strcompany,
                    salesOrderDetailBaseUrl);
            }
        }

        [HttpPost("{salesOrderId}/pay-by-credit-card")]
        [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_SalesOrder_MainInfo_Can_Pay_By_CreditCard)]
        public async Task<IActionResult> PayByCreditCardAsync([FromRoute] int salesOrderId, [FromForm] UploadMultipleDocumentRequest request)
        {
            var listFilesUpload = new List<PayByCreditCardFileUpload>();
            if (request.Files != null && request.Files.Count > 0)
            {
                foreach (var file in request.Files)
                {
                    if (file == null || file.Length == 0)
                        continue;

                    listFilesUpload.Add(new PayByCreditCardFileUpload
                    {
                        File = file.OpenReadStream(),
                        FileName = file.FileName,
                        Caption = request.Caption,
                        GeneratedFilename = GetStockImageFilename(file.FileName, salesOrderId),
                    });
                }
            }
            else
            {
                listFilesUpload.Add(new PayByCreditCardFileUpload
                {
                    File = null,
                    FileName = null,
                    Caption = request.Caption,
                    GeneratedFilename = null,
                });
            }

            string sectionName = request.SectionName.Split("_")[0].ToUpper();
            var response = await _mediator.Send(new PayByCreditCardCommand(listFilesUpload, salesOrderId, sectionName, UserId, ClientId));
            return Ok(response);
        }

        private static string GetStockImageFilename(string fileName, int stockNo)
        {
            string ext = Path.GetExtension(fileName);
            return $"{stockNo}_{DateTime.Now:yyyyMMddHHmmssfff}{Guid.NewGuid().ToString()}{ext}";
        }

        [HttpPut("export-approval/{exportApprovalId}/request-approval")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RequestApprovalExportApproval(int exportApprovalId, [FromBody] RequestApprovalExportApprovalRequest request)
        {
            await CheckUserPermission(salesOrderClientId: request.ClientId, pagePermission: SecurityFunction.Orders_SalesOrder_ExportApproval_RequestApproval);
            var v1redirectBaseUrl = _configuration[AppSettingKeys.GTv1SiteUrl];
            var v2redirectBaseUrl = RequestHelper.GetApplicationUrl(HttpContext.Request);
            var soDetailUrl = RedirectUrlHelper.GetUrlWithParams_SalesOrder(request.SalesOrderId);
            var result = await _mediator.Send(new RequestApprovalExportApprovalCommand()
            {
                LoginId = UserId,
                ExportApprovalId = exportApprovalId,
                RedirectBaseUrlV1 = v1redirectBaseUrl,
                RedirectBaseUrlV2 = v2redirectBaseUrl,
                SODetailUrl = soDetailUrl,
                ClientId = request.ClientId,
                Message = request.Message,
                Subject = request.Subject
            });
            return Ok(result);
        }

        [HttpGet("{salesOrderId}/euu-lines")]
        public async Task<IActionResult> GetAllSoLinesforEuu([FromRoute] int salesOrderId)
        {
            var query = new GetAllSoLinesforEuuQuery
            {
                IncludeInactive = true,
                SalesOrderId = salesOrderId
            };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("{salesOrderId}/notification-template")]
        public async Task<IActionResult> GetSONotificationTemplate(int salesOrderId)
        {
            var query = new GetNotifySOMessageTemplateQuery()
            {
                SalesOrderId = salesOrderId,
                Subject = _messageLocalizer["Notify Sales Order"],
                RedirectUrl = $"{RequestHelper.GetApplicationUrl(HttpContext.Request)}{RedirectUrlHelper.GetUrlWithParams_SalesOrder(salesOrderId)}"
            };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpPost("{salesOrderId}/notify")]
        public async Task<IActionResult> NotifySalesOrder([FromRoute] int salesOrderId, [FromBody] SendNewMessageRequest request)
        {
            var toLogins = request.To?.Where(t => t.Type == 2).Select(t => t.Value) ?? [];
            var toGroups = request.To?.Where(t => t.Type == 1).Select(t => t.Value) ?? [];
            var command = new CreateNotificationAndSendEmailCommand()
            {
                Body = Functions.ReplaceLineBreaks(request.Message),
                Subject = request.Subject,
                CompanyNo = request.CompanyId,
                ToGroups = [.. toGroups],
                ToLogins = [.. toLogins],
                LoginEmail = LoginEmail,
                LoginId = UserId,
                Sender = _sessionManager.LoginFullName
            };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
    }
}
