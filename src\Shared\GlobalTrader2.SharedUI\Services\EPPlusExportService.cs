﻿using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.SharedUI.Interfaces;
using System.Data;
using OfficeOpenXml;
using Microsoft.Extensions.Localization;
using OfficeOpenXml.Style;
using System.Text;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.SalesOrder;

namespace GlobalTrader2.SharedUI.Services
{
    public class EPPlusExportService : IExportService
    {
        private readonly IStringLocalizer<CommonResources> _commonLocalizer;
        private const int MediumFontSize = 9;
        private const string NoteHeaderRange = "A1:AE1";
        private const string RequestHeaderRange = "A2:G2";
        private const string SupplierHeaderRange = "H2:AE2";
        private const string DefaultFont = "Tahoma,Arial,Sans-serif";
        private const string ContentFont = "Calibri";
        private static readonly string NoteColor = "#f36262";
        private static readonly string RequestHeaderBgColor = "#76b2f1";
        private static readonly string SupplierHeaderBgColor = "#b3eca7";
        private static readonly string BomExportNoteKey = "BOM Export Note";
        private static readonly string BomExportNoBidKey = "BOM Export If No Bid";
        private static readonly string BomExportMultipleKey = "BOM Export If Multiple Sourcing";

        public EPPlusExportService(IStringLocalizer<CommonResources> commonLocalizer)
        {
            _commonLocalizer = commonLocalizer;
        }

        public byte[] ExportTodoItems(IEnumerable<ToDoListTaskReadModel> todoItems)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add(_commonLocalizer["ToDoList"]);

                DataTable dataTable = new DataTable("ToDoList");

                dataTable.Columns.Add(_commonLocalizer["Task Date From"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Task Date To"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Task Reminder Date"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Task Title"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Task Type"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Task Category"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Task Status"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Customer Name"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Salesperson"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Quote Number"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));

                foreach (var todoItem in todoItems)
                {
                    var values = new object?[10];

                    values[0] = todoItem.TaskDateFrom?.ToString("dd/MM/yyyy");
                    values[1] = todoItem.TaskDateTo?.ToString("dd/MM/yyyy");
                    values[2] = todoItem.TaskReminderDate?.ToString("dd/MM/yyyy");
                    values[3] = todoItem.TaskTitle;
                    values[4] = todoItem.TaskType;
                    values[5] = todoItem.ToDoCategoryName;
                    values[6] = todoItem.TaskStatus;
                    values[7] = todoItem.CustomerName;
                    values[8] = todoItem.SalesPersonName;
                    values[9] = todoItem.QuoteNumber;

                    dataTable.Rows.Add(values);
                }

                worksheet.Cells["A1"].LoadFromDataTable(dataTable, true);

                worksheet.Cells["A1:J1"].Style.Font.Bold = true;

                return package.GetAsByteArray();
            }
        }

        public byte[] ExportToExcelHUBRFQ(IEnumerable<BomCustomerRequirementExportDto> bomItems)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("HUBRFQResult");
            worksheet.Cells.Style.Font.Name = ContentFont;

            AddTableHeaderHUBRFQ(worksheet);

            DataTable dataTable = new DataTable("HUBRFQResult");

            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT HUBRFQ No"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT REQUIREMENT"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT CLIENT NO."], typeof(int));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT REQ. PART"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT Product"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT REQ. MANUFACTURER"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT REQ. QTY"], typeof(int));

            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT CUSTOMER REF NO."], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *SUPPLIER NAME"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *SUPPLIER PART NO."], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *Supplier Cost"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT ROHS"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *MANUFACTURER"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT DATE CODE"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT PACKAGE"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *OFFERED QTY."], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT OFFER STATUS"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT SPQ"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT FACTORY SEALED"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT QTY IN STOCK"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT MOQ"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT LAST TIME BUY"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *CURRENCY"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *BUY PRICE"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT *SELL PRICE"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT SHIPPING COST"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT LEADTIME"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT REGION"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT DELIVERY DATE"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT NOTES"], typeof(string));
            dataTable.Columns.Add(_commonLocalizer["HUBRFQ EXPORT MSL"], typeof(string));

            foreach (var bomItem in bomItems)
            {
                var values = new object?[31];

                values[0] = bomItem.HUBRFQNo;
                values[1] = bomItem.RequirementNumber;
                values[2] = bomItem.ClientNo;
                values[3] = bomItem.ReqPart;
                values[4] = bomItem.Product;
                values[5] = bomItem.ReqManufacturer;
                values[6] = bomItem.ReqQty;

                dataTable.Rows.Add(values);
            }

            worksheet.Cells["A2"].LoadFromDataTable(dataTable, true);

            return package.GetAsByteArray();
        }

        private void AddTableHeaderHUBRFQ(ExcelWorksheet ws)
        {
            ws.Row(1).Height = 50;
            ws.Row(1).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(1).Style.WrapText = true;

            var sb = new StringBuilder()
                .Append(_commonLocalizer[BomExportNoteKey]).Append('\n')
                .Append(_commonLocalizer[BomExportNoBidKey]).Append('\n')
                .Append(_commonLocalizer[BomExportMultipleKey]);

            ws.Cells[NoteHeaderRange].Merge = true;
            ws.Cells[NoteHeaderRange].Style.Font.Name = DefaultFont;
            ws.Cells[NoteHeaderRange].Value = sb.ToString();
            ws.Cells[NoteHeaderRange].Style.Font.Color.SetColor(System.Drawing.ColorTranslator.FromHtml(NoteColor));

            ws.Cells[RequestHeaderRange].Style.Font.Bold = true;
            ws.Cells[RequestHeaderRange].Style.Font.Name = DefaultFont;
            ws.Cells[RequestHeaderRange].Style.Fill.PatternType = ExcelFillStyle.Solid;
            ws.Cells[RequestHeaderRange].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml(RequestHeaderBgColor));
            ws.Cells[RequestHeaderRange].Style.Font.Size = MediumFontSize;

            ws.Cells[SupplierHeaderRange].Style.Font.Bold = true;
            ws.Cells[SupplierHeaderRange].Style.Font.Name = DefaultFont;
            ws.Cells[SupplierHeaderRange].Style.Fill.PatternType = ExcelFillStyle.Solid;
            ws.Cells[SupplierHeaderRange].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml(SupplierHeaderBgColor));
            ws.Cells[SupplierHeaderRange].Style.Font.Size = MediumFontSize;
        }

        public byte[] ExportToExcelSalesOrder(IEnumerable<SalesOrderLineExportDto> items)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add(_commonLocalizer["SalesOrderResult"]);
                worksheet.Cells.Style.Font.Name = "Calibri";

                DataTable dataTable = new DataTable("SalesOrderResult");

                dataTable.Columns.Add(_commonLocalizer["Sales Order No"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Part No"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Customer PO No"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Qty Ordered"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Qty Shipped"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Qty In Stock"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Company"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Contact"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Ordered"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Promised"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Status"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));
                dataTable.Columns.Add(_commonLocalizer["Contract No"], Nullable.GetUnderlyingType(typeof(string)) ?? typeof(string));

                foreach (var item in items)
                {
                    var values = new object?[12];

                    values[0] = item.SalesOrderNo;
                    values[1] = item.PartNo;
                    values[2] = item.CustomerPoNo;
                    values[3] = item.QtyOrdered;
                    values[4] = item.QtyShipped;
                    values[5] = item.QtyInStock;
                    values[6] = item.Company;
                    values[7] = item.Contact;
                    values[8] = item.Ordered;
                    values[9] = item.Promised;
                    values[10] = item.Status;
                    values[11] = item.ContractNo;

                    dataTable.Rows.Add(values);
                }

                worksheet.Cells["A1"].LoadFromDataTable(dataTable, true);

                return package.GetAsByteArray();
            }
        }
    }
}
