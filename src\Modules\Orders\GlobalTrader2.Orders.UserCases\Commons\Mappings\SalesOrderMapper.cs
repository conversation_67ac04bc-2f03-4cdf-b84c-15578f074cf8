﻿using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.Company.SalesInfo;
using GlobalTrader2.Dto.Manufacturers.Document;
using GlobalTrader2.Dto.SalesOrder;

namespace GlobalTrader2.Orders.UserCases.Commons.Mappings
{
    public class SalesOrderMapper : Profile
    {
        public SalesOrderMapper()
        {
            CreateMap<CompanySalesInfoReadModel, DefaultSalesInfoDto>();
            CreateMap<SalesOrderDetailsReadModel, SaleOrderForSelectDto>();
            CreateMap<SalesOrderDetailsReadModel, SaleOrderMainInfoDto>()
                .ForMember(x => x.CustomerNo, d => d.MapFrom(x => x.CompanyNo))
                .ForMember(x => x.CustomerName, d => d.MapFrom(x => x.CompanyName))
                .ForMember(x => x.SalesmanNo, d => d.MapFrom(x => x.Salesman))
                .ForMember(x => x.Salesman2No, d => d.MapFrom(x => x.Salesman2))
                .ForMember(x => x.Salesman, d => d.MapFrom(x => x.SalesmanName))
                .ForMember(x => x.Salesman2, d => d.MapFrom(x => x.Salesman2Name))
                .ForMember(x => x.Salesman2No, d => d.MapFrom(x => x.Salesman2))
                .ForMember(x => x.SalesmanDivision, d => d.MapFrom(x => $"{x.SalesmanName}, {x.DivisionName}"))
                .ForMember(x => x.ShipToNo, d => d.MapFrom(x => x.ShipToAddressNo))
                .ForMember(x => x.IsClosed, d => d.MapFrom(x => x.Closed))
                .ForMember(x => x.IsPaid, d => d.MapFrom(x => x.Paid))
                .ForMember(x => x.SalesOrderValue, d => d.MapFrom(x => x.TotalValue))
                .ForMember(x => x.Currency, d => d.MapFrom(x => Functions.FormatCurrencyDescription(x.CurrencyDescription, x.CurrencyCode)))
                .ForMember(x => x.DateOrderedDesc, d => d.MapFrom(x => Functions.FormatDate(x.DateOrdered)))
                .ForMember(x => x.DateAuthorisedDesc, d => d.MapFrom(x => Functions.FormatDate(x.DateAuthorised)))
                .ForMember(x => x.ShippingAccountNo, d => d.MapFrom(x => x.Account))
                .ForMember(x => x.CustomerNotes, d => d.MapFrom(x => x.Instructions))
                .ForMember(x => x.InternalNotes, d => d.MapFrom(x => x.Notes))
                .ForMember(x => x.SentOrder, d => d.MapFrom(x => x.SentOrder > 0))
                .ForMember(x => x.ShippingCostDBValue, d => d.MapFrom(x => Convert.ToDecimal(x.ShippingCost)))
                .ForMember(x => x.FreightDBValue, d => d.MapFrom(x => Convert.ToDecimal(x.Freight)))
                .ForMember(x => x.ShippingNotetoWHonly, d => d.MapFrom(x => x.ShippingNotetoWHonly))
                .ForMember(x => x.OGEL_EndUserNotes, d => d.MapFrom(x => x.OGEL_EndUserNotes)) //need to review
                .ForMember(x => x.PurchasingNotes, d => d.MapFrom(x => x.PurchasingNotes))
                .ForMember(x => x.AS6081_Text, d => d.MapFrom(x => x.AS6081.HasValue && x.AS6081.Value ? "Yes" : "No"))
                .ForMember(x => x.AS6081_Value, d => d.MapFrom(x => x.AS6081.HasValue && x.AS6081.Value ? 1 : 0))
                .ForMember(x => x.OGEL_Required_Text, d => d.MapFrom(x => x.OGEL_Required.HasValue && x.OGEL_Required.Value ? "Yes" : "No"))
                .ForMember(x => x.OGEL_Required_Value, d => d.MapFrom(x => x.OGEL_Required.HasValue && x.OGEL_Required.Value ? 1 : 0))
                .ForMember(x => x.CountryWarningMessage, d => d.MapFrom(x => x.WarningText));//need to review
            CreateMap<SalesOrderForPageReadModel, SalesOrderForPageDto>();
            CreateMap<SalesOrderPDF, DocumentDto>()
               .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.SalesOrderPDFId))
               .ForMember(dest => dest.UpdatedByName, opt => opt.MapFrom(src => src.Login != null ? src.Login.EmployeeName : string.Empty))
               .ForMember(dest => dest.Caption, opt => opt.MapFrom(src => Functions.ReplaceLineBreaks(src.Caption ?? string.Empty)))
               .AfterMap((src, dest, context) =>
               {
                   var culture = context.Items["culture"]?.ToString();
                   dest.DateUploadString = src.DLUP != null ? Functions.FormatDateStaticBST((DateTime)src.DLUP, culture, false, true) : "";
               });

            CreateMap<SOPaymentInfo, DocumentDto>()
               .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.SOPaymentInfoId))
               .ForMember(dest => dest.UpdatedByName, opt => opt.MapFrom(src => src.Login != null ? src.Login.EmployeeName : string.Empty))
               .ForMember(dest => dest.Caption, opt => opt.MapFrom(src => Functions.ReplaceLineBreaks(src.ReceiptNo)))
               .ForMember(dest => dest.FileName, opt => opt.MapFrom(src => src.GeneratedFilename ?? string.Empty))
               .AfterMap((src, dest, context) =>
               {
                   var culture = context.Items["culture"]?.ToString();
                   dest.DateUploadString = Functions.FormatDateStaticBST((DateTime)src.CreatedDate, culture, false, true);
               });

            CreateMap<SalesOrderExcel, DocumentDto>()
              .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.SalesOrderExcelId))
              .ForMember(dest => dest.UpdatedByName, opt => opt.MapFrom(src => src.Login != null ? src.Login.EmployeeName : string.Empty))
              .ForMember(dest => dest.Caption, opt => opt.MapFrom(src => Functions.ReplaceLineBreaks(src.Caption ?? string.Empty)))
              .AfterMap((src, dest, context) =>
              {
                  var culture = context.Items["culture"]?.ToString();
                  dest.DateUploadString = src.DLUP != null ? Functions.FormatDateStaticBST((DateTime)src.DLUP, culture, false, true) : "";
              });
            CreateMap<SOAddVancePaymentnotificationReadModel, SOAddVancePaymentnotificationDto>()
                .ForMember(x => x.ToEmail, d => d.MapFrom(x => x.SalesmanEmailID))
                .ForMember(x => x.IsPaid, d => d.MapFrom(x => x.Paid));
            #region SalesOrderForPrint
            CreateMap<SalesOrderForPrintReadModel, SalesOrderForPrintDto>();
            #endregion
        }
    }
}
