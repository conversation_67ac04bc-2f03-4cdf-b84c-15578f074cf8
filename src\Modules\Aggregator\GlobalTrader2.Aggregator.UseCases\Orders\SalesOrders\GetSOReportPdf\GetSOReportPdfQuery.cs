﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.File;

namespace GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetSOReportPdf
{
    public class GetSOReportPdfQuery : IRequest<BaseResponse<FileUploadItem>>
    {
        public required int SalesOrderId { get; set; }
        public required int ClientId { get; set; }
        public required string LoginFullName { get; set; }
        public required string BaseDirectoryPath { get; set; }
    }
}
