﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportManualStock;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPO;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Queries
{
    public class GetSalesOrderLineReportPoHandlerTest
    {
        private readonly Mock<IBaseRepository<SalesOrderLineReportPoReadModel>> _salesOrderLinePoRepository;
        private readonly Mock<IMapper> _mapper;
        private readonly GetSalesOrderLineReportPoHandler _handler;
        private readonly IFixture _fixture = new Fixture();
        public GetSalesOrderLineReportPoHandlerTest()
        {
            _salesOrderLinePoRepository = new Mock<IBaseRepository<SalesOrderLineReportPoReadModel>>();
            _mapper = new Mock<IMapper>();
            _handler = new GetSalesOrderLineReportPoHandler(_salesOrderLinePoRepository.Object, _mapper.Object);
        }
        [Fact]
        public async Task GetSalesOrderLineReportPoHandler_ReturnValueTest()
        {
            _salesOrderLinePoRepository.Setup(z => z.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>())).ReturnsUsingFixture(_fixture);
            _mapper.Setup(z => z.Map<IReadOnlyList<SalesOrderLineReportPoDto>>(It.IsAny<IReadOnlyList<SalesOrderLineReportPoReadModel>>()))
                .ReturnsUsingFixture(_fixture);
            var request = _fixture.Create<GetSalesOrderLineReportPoQuery>();
            var result = await _handler.Handle(request, CancellationToken.None);
            Assert.NotNull(result);
            Assert.True(result.Success);
        }
    }
}
