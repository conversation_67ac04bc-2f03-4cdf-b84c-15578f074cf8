﻿namespace GlobalTrader2.Core.Domain.Entities
{
    public class SalesOrderLineReportPoStockReadModel
    {
        public int SalesOrderLineId { get; set; }
        public int SalesOrderNo { get; set; }
        public int? StockNo { get; set; }
        public int QuantityAllocated { get; set; }
        public int? GoodsInLineNo { get; set; }
        public int? GoodsInNo { get; set; }
        public string Part { get; set; } = string.Empty;
        public byte? ROHS { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? ManufacturerName { get; set; }
        public string? ManufacturerCode { get; set; }
        public string? DateCode { get; set; }
        public int? PurchaseOrderLineNo { get; set; }
        public double? PurchasePrice { get; set; }
        public int? ProductNo { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public int? PurchaseOrderNo { get; set; }
        public int? PurchaseOrderNumber { get; set; }
        public int? CurrencyNo { get; set; }
        public string? CurrencyCode { get; set; }
        public int SupplierNo { get; set; }
        public string? SupplierName { get; set; }
        public string? CountryName { get; set; }
        public bool? Duty { get; set; }
        public double? DutyRate { get; set; }
        public double ShipInCost { get; set; }
        public DateTime DateReceived { get; set; }
        public int? TermsNo { get; set; }
        public string? TermsName { get; set; }
        public char? Taxable { get; set; }
        public double? LandedCost { get; set; }
        public double? ClientLandedCost { get; set; }
        public string? SupplierType { get; set; }
        public string? SupplierPart { get; set; }
        public int? POSerialNo { get; set; }
        public int InternalPurchaseOrderNo { get; set; }
        public string? IPOSupplierName { get; set; }
        public string? IPOSupplierType { get; set; }
        public DateTime PODateOrdered { get; set; }
        public bool IsProdHazardous { get; set; }
        public bool IsOrderViaIPOonly { get; set; }
        public string? ECCNCode { get; set; }
        public bool IsECCNWarning { get; set; }
        public int? RestrictedMfrNo { get; set; }
        public bool? RestrictedMfrInactive { get; set; }

    }
}
