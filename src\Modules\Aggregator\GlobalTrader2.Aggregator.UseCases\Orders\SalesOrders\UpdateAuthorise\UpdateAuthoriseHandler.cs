﻿using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.UploadSalesOrderReportPdf;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyIPO;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifySaleperson;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifySales;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetSaleOrderMainInfo;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Orders.UserCases.Orders.InternalPurchaseOrder.Queries.GetIPOAfterSOChecked;

namespace GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.UpdateAuthorise
{
    public class UpdateAuthoriseHandler : IRequestHandler<UpdateAuthoriseCommand, BaseResponse<bool>>
    {
        private readonly IBaseRepository<object> _objectRepository;
        private readonly ISender _sender;
        public UpdateAuthoriseHandler(IBaseRepository<object> objectRepository, ISender sender)
        {
            _objectRepository = objectRepository;
            _sender = sender;
        }
        public async Task<BaseResponse<bool>> Handle(UpdateAuthoriseCommand request, CancellationToken cancellationToken)
        {
            var output = new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output };
            bool isPowerAppApproved = false;
            //create pdf here
            var updateAuthoriseParams = new List<SqlParameter>()
                    {
                        new SqlParameter("@SalesOrderId",SqlDbType.Int) {Value = request.SalesOrderId },
                        new SqlParameter("@AuthorisedBy",SqlDbType.Int) {Value = request.LoginId},
                        new SqlParameter("@Authorise",SqlDbType.Bit) {Value = request.IsAuthorise },
                        new SqlParameter("@Comment",SqlDbType.NVarChar) {Value = request.Comment  ?? (object)DBNull.Value},
                        new SqlParameter("@IsPowerAppApproved",SqlDbType.Bit) {Value = isPowerAppApproved },
                        new SqlParameter("@IsAllowReadyTOShip",SqlDbType.Bit) {Value = request.IsAllowReadyToShip },
                        output
                    };
            await _objectRepository.ExecuteSqlRawAsync($"{StoredProcedures.Update_SalesOrder_Authorise} " +
                $"@SalesOrderId, @AuthorisedBy, @Authorise, @Comment, @IsPowerAppApproved, @IsAllowReadyTOShip, @RowsAffected output"
                , updateAuthoriseParams.ToArray());
            var rowsAffected = (int)output.Value;
            if (request.IsAuthorise)
            {
                await _sender.Send(new UploadSalesOrderReportPdfCommand() { 
                      ClientId = request.ClientId
                    , SalesOrderId = request.SalesOrderId
                    , BaseDirectory = request.BaseDirectory
                    , LoginFullName = request.LoginName
                    ,LoginId = request.LoginId }, cancellationToken);
                await NotifyIPO(request.SalesOrderId
                    , request.CreateIPOSubject
                    , request.LoginEmail
                    , request.LoginId
                    , request.LoginName
                    ,request.HostUrl
                    ,request.PurchaseOrderUrl
                    ,request.InternalPurchaseOrderUrl
                    ,cancellationToken);
            }
            if (request.IsNotify && request.IsAuthorise)
            {
                var salesOrderInfo = await _sender.Send(new GetSaleOrderMainInfoQuery()
                {
                    LoginId = 0,
                    SaleOrderId = request.SalesOrderId,
                    ClientNo = 0,

                }, cancellationToken);
                if(salesOrderInfo.Success && salesOrderInfo.Data != null)
                {
                    var salesInfo = salesOrderInfo.Data;
                    var notifySalesCommand = new NotifySalesCommand()
                    {
                        SalesOrder = salesInfo,
                        LoginEmail = request.LoginEmail,
                        SOAuthorisedSubject = request.SOAuthoriseSubject ?? string.Empty,
                        LoginId = request.LoginId,
                        LoginName = request.LoginName,
                        Comment = request.Comment
                    };
                    await _sender.Send(notifySalesCommand, cancellationToken);
                }
            }
            return new BaseResponse<bool>()
            {
                Success = rowsAffected > 0,
                Data = rowsAffected > 0
            };
        }
        private async Task NotifyIPO(int salesOrderId, string createIPOSubject, string loginEmail, int LoginId, string loginName, string hostUrl, string purchaseOrderUrl, string internalPurchaseOrderUrl, CancellationToken cancellationToken)
        {
            var ipoQuery = await _sender.Send(new GetIpoAfterSoCheckedQuery() { SalesOrderId = salesOrderId }, cancellationToken);
            if (ipoQuery.Success && ipoQuery.Data != null && ipoQuery.Data.Any())
            {
                await _sender.Send(new NotifyIpoCommand()
                {
                    CreateIPOSubject = createIPOSubject,
                    IPOList = ipoQuery.Data,
                    LoginEmail = loginEmail,
                    LoginId = LoginId,
                    LoginName = loginName,
                    HostUrl = hostUrl,
                    PurchaseOrderUrl = purchaseOrderUrl,
                    InternalPurchaseOrderUrl = internalPurchaseOrderUrl

                }, cancellationToken);
            }
        }
    }
}
