﻿namespace GlobalTrader2.Core.Domain.Entities
{
    public class SalesOrderLineReportManualStockReadModel
    {
        public int SalesOrderLineId { get; set; }
        public int SalesOrderNo { get; set; }
        public int? StockNo { get; set; }
        public int QuantityAllocated { get; set; }
        public string Part { get; set; } = string.Empty;
        public int? ManufacturerNo { get; set; }
        public string? ManufacturerCode { get; set; }
        public string? DateCode { get; set; }
        public double LandedCost { get; set; }
        public int? ProductNo { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public int CurrencyNo { get; set; }
        public string CurrencyCode { get; set; } = string.Empty;
        public double? ResalePrice { get; set; }
        public string? SupplierPart { get; set; }
        public byte? ROHS { get; set; }
        public bool IsProdHazardous { get; set; }
        public bool IsOrderViaIPOonly { get; set; }
        public string? ECCNCode { get; set; }
        public bool IsECCNWarning { get; set; }
        public int? RestrictedMfrNo { get; set; }
        public bool? RestrictedMfrInactive { get; set; }
    }
}
