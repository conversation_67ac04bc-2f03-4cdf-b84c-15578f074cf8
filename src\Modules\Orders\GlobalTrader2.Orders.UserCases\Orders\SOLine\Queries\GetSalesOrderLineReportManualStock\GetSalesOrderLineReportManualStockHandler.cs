﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;
using Microsoft.IdentityModel.Tokens;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportManualStock
{
    public class GetSalesOrderLineReportManualStockHandler : IRequestHandler<GetSalesOrderLineReportManualStockQuery, BaseResponse<IReadOnlyList<SalesOrderLineReportManualStockDto>>>
    {
        private readonly IBaseRepository<SalesOrderLineReportManualStockReadModel> _salesOrderLinePoRepository;
        private readonly IMapper _mapper;
        public GetSalesOrderLineReportManualStockHandler(IBaseRepository<SalesOrderLineReportManualStockReadModel> salesOrderLinePoRepository, IMapper mapper)
        {
            _salesOrderLinePoRepository = salesOrderLinePoRepository;
            _mapper = mapper;
        }
        public async Task<BaseResponse<IReadOnlyList<SalesOrderLineReportManualStockDto>>> Handle(GetSalesOrderLineReportManualStockQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IReadOnlyList<SalesOrderLineReportManualStockDto>>();

            var paramters = new List<SqlParameter>()
            { new SqlParameter("@SalesOrderLineId", SqlDbType.Int) { Value = request.SalesOrderLineId } };
            var queryResult = await _salesOrderLinePoRepository.SqlQueryRawAsync
                ($"{StoredProcedures.Get_All_SalesOrderLine_ReportManualStock} @SalesOrderLineId", paramters.ToArray());
            if (queryResult.Any())
            {
                response.Data = _mapper.Map<IReadOnlyList<SalesOrderLineReportManualStockDto>>(queryResult);
            }
            response.Success = true;
            return response;
        }
    }
}
