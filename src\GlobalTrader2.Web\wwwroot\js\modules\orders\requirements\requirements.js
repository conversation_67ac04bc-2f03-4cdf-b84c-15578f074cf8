﻿import { LiteDatatable } from "../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#"
import { FilterTableSectionBox } from "../../../components/base/filter-table-section-box.component.js?v=#{BuildVersion}#"
import { SectionBox } from "../../../components/base/section-box.component.js?v=#{BuildVersion}#"
import { inputFilterDefinition } from "./input-filter-definition.js?v=#{BuildVersion}#"
import { ROHSHelper } from "../../../helper/rohs-helper.js?v=#{BuildVersion}#";
import { RequirementTalbeFilterComponent } from "../requirements/components/requirement-table-filter.js?v=#{BuildVersion}#";
import { PartTableButton } from "../../../components/button/part-table-button.js?v=#{BuildVersion}#";
import { ButtonHelper } from '../../../helper/button-helper.js?v=#{BuildVersion}#';
import { getUrl } from '../../../helper/url-helper.js?v=#{BuildVersion}#';
import { DataListNuggetType } from '../../../config/data-list-nugget-type-config.js?v=#{BuildVersion}#'
import { KUBAssistantComponent } from "../../../components/kub-assistant/kub-assistant.component.js?v=#{BuildVersion}#";
const save = window.localizedStrings.save;
const cancel = window.localizedStrings.cancel;
const state = {
    tabId: 0, // default currentTab
    tabDic: {
        0: "my-tab",
        1: "team-tab",
        2: "division-tab",
        3: "company-tab",
    },
    filterStateType: DataListNuggetType.CustomerRequirement,
    requirementsTable: null,
    filter: null,
    sectionBox: null,
    filterSectionBox: null,
    addRequirementDialogInited: false,
    lastFilter: null, // state already made a call
    isGSA: isGSA,
    isGlobalUser: isGlobalUser,
    kubAssistant: null,
    kubQuoteTable: null,
    currentSelectedRequirement: null
}

$(async () => {

    const preferences = await getPreferences();
    state.preferences = preferences;

    function registerTabEvents() {
        $(document).on('click', '#requirement-nav-tabs-wrapper button', async (e, data) => {
            const ignoreSave = data?.notResetPageRequired
            setTimeout(function () {
                openningTab($(e.target).attr("tabId"), ignoreSave, !data?.notResetPageRequired);
            }, 100);
        })
    }

    function prepareDataBeforeReload(filterData) {
        // for some cases, we need to construct to a new object before sending it to server.
        // for example, an input where user need to chose a comparison operator a long with raw value is 10. filteredData only return 10 and the selected operator
        // While the server expects 2 fields qualityLo, qualityHi.
        let requestData = {
            viewLevelList: state.tabId,
            partSearch: !shouldInclude(filterData.Part) ? null : filterData.Part?.value,
            partWatch: filterData.PartWatch?.isOn ?? false,
            includeClosed: filterData.IncludeClosed?.isOn ?? false,
            recentOnly: filterData.RecentOnly?.isOn ?? false,
            cmSearch: !shouldInclude(filterData.CMName) ? null : filterData.CMName?.value,
            contactSearch: !shouldInclude(filterData.Contact) ? null : filterData.Contact?.value,
            bomNameSearch: !shouldInclude(filterData.BOMName) ? null : filterData.BOMName?.value,
            receivedDateFrom: !shouldInclude(filterData.ReceivedDateFrom) ? null : !filterData.ReceivedDateFrom?.value ? null : filterData.ReceivedDateFrom.value,
            receivedDateTo: !shouldInclude(filterData.ReceivedDateTo) ? null : !filterData.ReceivedDateTo?.value ? null : filterData.ReceivedDateTo.value,
            datePromisedFrom: !shouldInclude(filterData.DatePromisedFrom) ? null : !filterData.DatePromisedFrom?.value ? null : filterData.DatePromisedFrom.value,
            datePromisedTo: !shouldInclude(filterData.DatePromisedTo) ? null : !filterData.DatePromisedTo?.value ? null : filterData.DatePromisedTo.value,
            industryName: !shouldInclude(filterData.IndustryType) ? null : (filterData.IndustryType?.value == 0 ? null : filterData.IndustryType?.value),
            customerRequirementNoLo: !shouldInclude(filterData.CReqNo) ? null : filterData.CReqNo.low,
            customerRequirementNoHi: !shouldInclude(filterData.CReqNo) ? null : filterData.CReqNo.hi,
            totalLo: !shouldInclude(filterData.TotalValue) ? null : filterData.TotalValue.low,
            totalHi: !shouldInclude(filterData.TotalValue) ? null : filterData.TotalValue.hi,
            selectedClientNo: filterData.ClientName?.value || null,
            salesman: state.tabId == 0 ? null : !shouldInclude(filterData.Salesman) ? null : (filterData.Salesman?.value == 0 ? null : filterData.Salesman?.value),
            bomCode: !shouldInclude(filterData.HUBRFQ) ? null : filterData.HUBRFQ?.value,
            reqStatus: !shouldInclude(filterData.StatusREQ) ? null : (filterData.StatusREQ?.value == 0 ? null : filterData.StatusREQ?.value),
            as6081: !shouldInclude(filterData.AS6081) ? null : handleAS6081(filterData.AS6081?.value)
        };

        function shouldInclude(input) {
            return input?.isOn && input.isShown;
        }

        if (state.isGSA) {
            state.lastFilter = {
                clientName: state.filter.getInputMetadataByName('ClientName')
            }
        }
        function handleAS6081(input) {
            return input == 0 ? null : input == 1;
        }
        return requestData;
    }

    async function openningTab(tabId, ignoreSave, resetPageRequired) {
        // this section for example & testing
        //params for testing
        state.tabId = tabId;
        hideSalesman(state.tabId == 0);
        const data = state.filter.getAppliedValues();
        const convertedData = prepareDataBeforeReload(data)
        if (resetPageRequired) {
            state.requirementsTable.resetPage();
        }
        convertedData._ignoredSave = ignoreSave;
        await state.requirementsTable.reloadAsync(convertedData);
    }

    function hideSalesman(isMyTab) {
        const inputs = state.filter.getInputs();
        isMyTab ? inputs["Salesman"].instance.wrapper.hide() : inputs["Salesman"].instance.wrapper.show();
    }

    function activeTab(tabId) {
        state.tabId = tabId;
        let tab = state.tabDic[tabId];

        $(`#${tab}`).trigger('click', {
            notResetPageRequired: true
        });
    }

    function shouldHighlightCompany() {
        if (!state.isGlobalUser) {
            const clientName = state.lastFilter?.clientName;
            return clientName?.isShown && clientName?.isOn && clientName.value;
        }
        return false;
    }

    async function getPreferences() {
        const response = await GlobalTrader.ApiClient.getAsync(`/user-account/profile/preferences`, {}, {});
        // handle error?
        return response?.data;
    }

    async function initFilterTableSectionBoxAsync() {
        state.requirementsTable = new LiteDatatable('#customerRequirementTbl', {
            serverSide: true,
            ajax: {
                url: '/api/orders/customer-requirements/list',
                type: 'POST',
            },
            ordering: true,
            pageConfig: {
                pageSize: state.preferences.defaultListPageSize
            },
            columns: [
                {
                    data: 'customerRequirementId',
                    name: 'CustomerRequirementId',
                    visible: false
                },
                {
                    title: `<div style="border: none !important; margin-bottom: 20px; padding-bottom: 8px;">${localizedTitles.no}</div>`,
                    className: 'text-wrap text-break header-custom',
                    data: 'customerRequirementNumber',
                    name: 'CustomerRequirementNumber',
                    width: "8%",
                    orderSequence: ["desc", "asc"],
                    render: function (data, type, row) {
                        return `
                        ${row.customerRequirementNumber ? $('<a>').attr('href', `/Orders/CustomerRequirement/Details?req=${row.customerRequirementId}`).addClass('dt-hyper-link').text(row.customerRequirementNumber).prop('outerHTML') + '<br>' : '<br>'}
                    `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${localizedTitles.partNo}</div>${localizedTitles.manufacturer}`,
                    className: 'text-wrap text-break header-custom',
                    data: 'part',
                    name: 'PartNo',
                    width: "17%",
                    orderSequence: ["desc", "asc"],
                    render: function (data, type, row) {
                        return `${ROHSHelper.writePartNo(row.part, row.rohs)} <br>
                    ${row.manufacturerCode ? $('<a>').attr('href', `/Contact/Manufacturers/Details?mfr=${row.manufacturerNo}`).addClass('dt-hyper-link').text(row.manufacturerCode).prop('outerHTML') + '<br>' : '<br>'}
                `;
                    }
                },
                {
                    data: 'quantity',
                    name: 'Quantity',
                    title: `<div style="border: none !important; margin-bottom: 20px; padding-bottom: 8px;">${localizedTitles.quantity}</div>`,
                    className: 'text-wrap text-break header-custom',
                    width: "10%",
                    orderSequence: ["desc", "asc"],
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${localizedTitles.company}</div>${localizedTitles.contact}`,
                    className: 'text-wrap text-break header-custom',
                    orderable: false,
                    data: 'contactName',
                    width: "17%",
                    render: function (data, type, row) {
                        return `
                    ${row.companyName ? $('<a>').attr('href', GlobalTrader.PageUrlHelper.Get_URL_Company(row.companyNo)).addClass('dt-hyper-link').css('background-color', shouldHighlightCompany() ? 'yellow' : '').text(row.companyName).prop('outerHTML') + '<br>' : '<br>'}
                    ${row.contactName ? $('<a>').attr('href', GlobalTrader.PageUrlHelper.Get_URL_Contact(row.contactNo)).addClass('dt-hyper-link').text(row.contactName).prop('outerHTML') + '<br>' : '<br>'}
                `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${localizedTitles.salesperson}</div>${localizedTitles.industryType}`,
                    className: 'text-wrap text-break header-custom',
                    data: 'salesmanName',
                    name: 'SalesmanName',
                    width: "14%",
                    orderSequence: ["desc", "asc"],
                    render: function (data, type, row) {
                        return `${row.salesmanName ? row.salesmanName : ""} <br>
                    ${row.industryName ? row.industryName : ""}`;
                    }

                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${localizedTitles.receivedDate}</div>${localizedTitles.promised}`,
                    className: 'text-wrap text-break header-custom',
                    data: 'receivedDate',
                    name: 'ReceivedDate',
                    width: "12%",
                    orderSequence: ["desc", "asc"],
                    render: function (data, type, row) {
                        return `${row.receivedDate} <br> 
                    ${row.datePromised}
                `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${localizedTitles.HUBRFQ}</div>${localizedTitles.hubStatus}`,
                    className: 'text-wrap text-break header-custom',
                    data: 'bomName',
                    name: 'HUBRFQ',
                    width: "10%",
                    orderable: false,
                    render: function (data, type, row) {
                        return `
                       ${row.bomName ? $('<a>').attr('href', `/Orders/HUBRFQ/Details?BOM=${row.bomId}`).addClass('dt-hyper-link').text(row.bomName).prop('outerHTML') + '<br>' : '<br>'}
                       ${row.status ? row.status : ""}
                    `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${localizedTitles.totalValue}</div>${localizedTitles.totalInBase}`,
                    className: 'text-wrap text-break header-custom',
                    orderSequence: ["desc", "asc"],
                    width: "12%",
                    data: 'totalValue',
                    name: 'TotalValue',
                    render: function (data, type, row) {
                        return `
                        ${row.totalValue ? `${row.totalValue} ${row.currencyCode}` : ""}<br>
                        ${row.totalInBase ? `${row.totalInBase} ${GlobalTrader.StringHelper.setCleanTextValue(clientCurrencyCode)}` : ""}
                    `;
                    }
                },

            ],
            rowId: 'customerRequirementId',
        })

        let inputs = inputFilterDefinition;

        if (!state.isGSA) {
            inputs = inputs.filter(x => x.name != 'ClientName');
        }

        state.filter = new RequirementTalbeFilterComponent("#filter-section-wrapper", "Filter Results", {
            inputConfigs: inputs
            //templateId:'my-template' // template insert input to filter
        });

        state.sectionBox = new SectionBox('#customer-requirement-box', {
            loadingContentId: state.requirementsTable.getContainerId() // only table should be hidden while processing api requests
        }, {
            enableFilterButton: false
        });

        state.filterSectionBox = new FilterTableSectionBox(state.requirementsTable, state.filter, state.sectionBox, {
            prepareDataBeforeReload: prepareDataBeforeReload
        });

        state.filterSectionBox.registerHiddenButtonFilterEvents();

        state.sectionBox.setLock(state.preferences.saveDLNState);
        state.filterSectionBox.setFilterStateType(state.filterStateType)
        await state.filterSectionBox.initAsync();

        setupTableEventListener();

        activeTab(currentTab);
    }

    async function initKubAssistanceAsync() {
        const response = await GlobalTrader.ApiClient.getAsync(`/orders/kub-assistance/setting`, {}, {});
        if (response?.data?.enabled) {
            initKUBAssistantChatBot();
            initKubAssistantQuoteTable();
            setupKubReadMoreEventListener();
        }
    }

    async function initAsync() {
        registerTabEvents();
        await initFilterTableSectionBoxAsync();
        await initKubAssistanceAsync();
        
        const appliedCount = state.filterSectionBox.countAppliedFilters(state.tabId);
        updateFilterAppliedMessage(appliedCount);
    }

    await initAsync();

    function checkClientNameAndSalesMan() {
        const inputs = state.filter.getInputs();
        const clientName = inputs.ClientName?.instance;
        const salesMan = inputs.Salesman?.instance;

        if (!clientName || !salesMan) return;

        const clientNameInitValue = clientName.getValue().value;
        if (clientNameInitValue && state.tabId !== 0) {
            salesMan.element.dropdown("reload", null, { clientNo: clientNameInitValue });
        }

        clientName.element.on('change', (event) => {
            salesMan.element.dropdown("reset");
            const value = clientName.getValue().value;
            if (value && state.tabId !== 0) {
                salesMan.element.dropdown("reload", null, { clientNo: event.target.value });
            } else {
                salesMan.element.dropdown("reload", null, { clientNo: null });
            }
        });
    }
    function updateFilterAppliedMessage(appliedCount) {
        let message;
        if (appliedCount === 0) {
            message = `${localizedTitles.no} ${localizedTitles.filter} ${localizedTitles.applied}`;
        } else {
            const filterLabel = appliedCount === 1 ? localizedTitles.filter : localizedTitles.filters;
            message = `${appliedCount} ${filterLabel} ${localizedTitles.applied}`;
        }
        state.filter.container.find('p[name="count-filter-applied"]').text(message);
    }

    function countFilterApplied(triggeredEvent) {
        const inputs = state.filter.getInputs();

        const appliedCount = (triggeredEvent === "off.mtf")
            ? 0
            : Object.values(inputs).reduce((count, input) => {
                const isSalesmanOnMyTab = (input.instance?.name === "Salesman" && state.tabId == 0);
                const isApplied = Boolean(input.inputValue) && input.instance?.isChecked?.();
                if (isSalesmanOnMyTab) return count;
                return count + (isApplied ? 1 : 0);
            }, 0);

        return appliedCount;
    }

    checkClientNameAndSalesMan();

    new PartTableButton("#print-req-enquiry-form", function () {
        location.href = getUrl(ButtonHelper.URL_Orders_Customer_Requirement_Print());
    })

    let isCancel = false;
    state.filter.on('cancel.mtf', () => {
        isCancel = true;
    })

    state.filterSectionBox.on('processed.mftsb', () => {
        setTimeout(() => {
            if (!isCancel) {
                const latestAppliedCount = state.filterSectionBox.countAppliedFilters(state.tabId);
                updateFilterAppliedMessage(latestAppliedCount);
            } else {
                isCancel = false;
            }
        }, 0)

    });
});

function setupKubReadMoreEventListener() {
    const toggleBtn = document.getElementById("kub-read-more-less-btn");
    const moreData = document.getElementById("kub-assistant-more-data-wrapper");
    const icon = toggleBtn.querySelector("img");
    const text = toggleBtn.querySelector("span");

    $('#kub-read-more-less-btn').button().on('click', async () => {
        const isExpanded = !moreData.classList.contains("d-none");

        if (isExpanded) {
            // Collapse
            moreData.classList.add("d-none");
            icon.src = "/img/icons/plus.svg";
            icon.alt = "Add icon";
            text.textContent = "Read more";
        } else {
            // Expand
            moreData.classList.remove("d-none");
            icon.src = "/img/icons/minus.svg";
            icon.alt = "Remove icon";
            text.textContent = "Read less";

            await loadMoreKubAssistantData();
        }
    })
}

function collapseKubMoreDataSection() {
    const toggleBtn = document.getElementById("kub-read-more-less-btn");
    const moreData = document.getElementById("kub-assistant-more-data-wrapper");
    const icon = toggleBtn.querySelector("img");
    const text = toggleBtn.querySelector("span");

    moreData.classList.add("d-none");
    icon.src = "/img/icons/plus.svg";
    icon.alt = "Add icon";
    text.textContent = "Read more";
}

function setupTableEventListener() {
    state.requirementsTable.on('select.mdt', async function (e, dt, type, indexes) {
        if (type === 'row') {
            state.currentSelectedRequirement = dt.row(indexes[0]).data();
            let rowHtml = $(dt.row(indexes[0]).node()).prop('outerHTML');
            // Remove 'selected' class from the HTML string
            rowHtml = rowHtml.replace(/\s?selected\b/g, '');
            rowHtml = rowHtml.replace(/\s?hover-row\b/g, '');
            $('#kub-chat-table-body').html(rowHtml);
            if (state.kubAssistant) {
                if (!state.kubAssistant.enabled) {
                    state.kubAssistant.enable();
                }

                collapseKubMoreDataSection();
                await Promise.all([
                    refreshLyticaApiData(state.currentSelectedRequirement.part, state.currentSelectedRequirement.manufacturerCode, state.currentSelectedRequirement.manufacturerNo, ""),
                    initKubAssistantData(state.currentSelectedRequirement)
                ]);
            }
        }
    });

    state.requirementsTable.on("preXhr.dt", async function () {
        if (state.kubAssistant?.enabled) {
            state.currentSelectedRequirement = null;
            state.kubAssistant.disable();
        }
    });
}

async function refreshLyticaApiData(partNumber, mfrCode, mfrNo, mfrName) {
    if (!partNumber || !mfrNo) {
        return;
    }

    const header = {};
    const data = {
        partNumber,
        mfrCode,
        mfrNo,
        mfrName
    };
    await GlobalTrader.ApiClient.postAsync(`/orders/customer-requirements/refresh-lytica-api-data`, data, header);
}

async function initKubAssistantData(rowData) {
    $("#kub-assistant-selected-part-no").text(rowData.part);
    $("#kub-assistant-selected-company").text(`"${rowData.companyName}"`);
    $("#kub-assistant-main-products-part-no").text(rowData.part);

    await GlobalTrader.ApiClient.postAsync("/orders/kub-assistance/start-cache-for-page", { PartNo: rowData.part, CustomerReqId: rowData.customerRequirementId }, {});
    const [avgPriceDetail, totalLineInvoiceDetail] = await Promise.all([
        GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/avg-price-details", { partNo: rowData.part }, {}),
        GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/total-line-invoice-details", { partNo: rowData.part, customerReqId: rowData.customerRequirementId }, {})
    ]);

    $("#kub-average-price-last-12months").text(avgPriceDetail?.data?.averagePriceOfPartLast12Months ?? "");
    $("#kub-last-updated-date").text(avgPriceDetail?.data?.lastUpdatedDate ?? "");
    $("#total-line-invoiced-in-12months").text(totalLineInvoiceDetail?.data?.totalLineInvoiceDetail ?? "0");    
}

async function loadMoreKubAssistantData() {
    state.kubQuoteTable.clear();
    $('#main-product-group-details-list').empty();

    const [ topRecentQuoteDetails, mainProductGroupDetails ] = await Promise.all([
        GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/last-top-recent-quote-details", { partNo: state.currentSelectedRequirement.part, customerReqId: state.currentSelectedRequirement.customerRequirementId }, {}),
        GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/main-product-group-details", { partNo: state.currentSelectedRequirement.part }, {}),
    ]);

    if (topRecentQuoteDetails?.data) {
        state.kubQuoteTable.rows.add(topRecentQuoteDetails?.data).draw();
    }
    if (mainProductGroupDetails?.data) {
        $.each(mainProductGroupDetails?.data, function (index, value) {
            $('#main-product-group-details-list').append('<li>' + value.mainProductGroup + '</li>');
        });
    }
}

function initKUBAssistantChatBot() {
    state.kubAssistant = new KUBAssistantComponent('kub-assistant-container', {
        enabled: false
    });

    if (state.kubAssistant.events.modalClosed == null) {
        state.kubAssistant.on("modalClosed", () => {
            collapseKubMoreDataSection();
        });
    }
}

function initKubAssistantQuoteTable() {
    state.kubQuoteTable = $('#kub-assistant-quote-table').DataTable({
        language: {
            emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
            zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
        },
        select: false,
        paging: false,
        ordering: false,
        searching: false,
        scrollCollapse: true,
        info: false,
        columns: [
            {
                title: 'Quote',
                data: 'quoteNumber',
                type: 'string',
                className: 'dt-left dt-head-left dt-type-string',
                width: "10%",
                render: function (data, type, row) {
                    return `
                        ${!isNaN(row.quoteId) ? $('<a target="_blank">').attr('href', `/Orders/Quotes/Details?qt=${row.quoteId}`).addClass('dt-hyper-link').text(row.quoteNumber).prop('outerHTML') : row.quoteId}
                    `;
                }
            },
            {
                title: 'Quote Date',
                data: 'quoteDate'
            },
            {
                title: 'Quantity',
                type: 'string',
                data: 'quantity'
            },
            {
                title: 'Unit price',
                data: 'unitPrice'
            },
            {
                title: 'Buy price',
                data: 'buyPrice'
            },
            {
                title: 'Total profit',
                data: 'profit'
            }
        ]
    });
}
