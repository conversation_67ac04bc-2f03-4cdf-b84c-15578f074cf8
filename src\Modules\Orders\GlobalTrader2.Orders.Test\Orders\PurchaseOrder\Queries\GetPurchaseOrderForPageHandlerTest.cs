﻿using AutoMapper;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Commons.Mappings;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderForPage;
using Microsoft.Data.SqlClient;
using Moq;
using System.Data;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Queries
{
    public class GetPurchaseOrderForPageHandlerTest
    {
        private readonly Mock<IBaseRepository<PurchaseOrderForPageReadModel>> _mockRepository;
        private readonly IMapper _mapper;
        private readonly GetPurchaseOrderForPageHandler _handler;

        public GetPurchaseOrderForPageHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<PurchaseOrderForPageReadModel>>();
            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new PurchaseOrderMapper());
            });
            _mapper = mappingConfig.CreateMapper();
            _handler = new GetPurchaseOrderForPageHandler(_mockRepository.Object, _mapper);
        }

        [Fact]
        public async Task Handle_WithValidPurchaseOrderId_ReturnsSuccessResponseWithMappedData()
        {
            // Arrange
            var purchaseOrderId = 123;
            var query = new GetPurchaseOrderForPageQuery(purchaseOrderId);

            var mockReadModel = new List<PurchaseOrderForPageReadModel>
            {
                new PurchaseOrderForPageReadModel
                {
                    PurchaseOrderId = purchaseOrderId,
                    PurchaseOrderNumber = 456789,
                    ClientNo = 1,
                    CompanyNo = 100,
                    StatusNo = 1,
                    Closed = false,
                    CompanyName = "Test Company",
                    CompanyNameForSearch = "TEST COMPANY",
                    IsPDFAvailable = true,
                    TeamNo = 5,
                    DivisionNo = 2,
                    Buyer = 10,
                    IPOClientNo = 15,
                    IsPOHub = false,
                    ClientName = "Test Client",
                    ClientBaseCurrencyCode = "USD",
                    IsPORPDFAvailable = true
                }
            };

            _mockRepository.Setup(x => x.SqlQueryRawAsync(
                It.Is<string>(sql => sql.Contains(StoredProcedures.Select_PurchaseOrder_for_Page) && sql.Contains("@PurchaseOrderId")),
                It.Is<SqlParameter[]>(parameters =>
                    parameters.Length == 1 &&
                    parameters[0].ParameterName == "@PurchaseOrderId" &&
                    parameters[0].SqlDbType == SqlDbType.Int &&
                    (int)parameters[0].Value == purchaseOrderId)))
                .ReturnsAsync(mockReadModel);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(purchaseOrderId, result.Data.PurchaseOrderId);
            Assert.Equal(456789, result.Data.PurchaseOrderNumber);
            Assert.Equal(1, result.Data.ClientNo);
            Assert.Equal("Test Company", result.Data.CompanyName);
            Assert.Equal("USD", result.Data.ClientBaseCurrencyCode);
            Assert.True(result.Data.IsPDFAvailable);
            Assert.False(result.Data.IsPOHub);

            _mockRepository.Verify(x => x.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithNonExistentPurchaseOrderId_ReturnsUnsuccessfulResponse()
        {
            // Arrange
            var purchaseOrderId = 999;
            var query = new GetPurchaseOrderForPageQuery(purchaseOrderId);

            _mockRepository.Setup(x => x.SqlQueryRawAsync(
                It.Is<string>(sql => sql.Contains(StoredProcedures.Select_PurchaseOrder_for_Page) && sql.Contains("@PurchaseOrderId")),
                It.Is<SqlParameter[]>(parameters =>
                    parameters.Length == 1 &&
                    parameters[0].ParameterName == "@PurchaseOrderId" &&
                    parameters[0].SqlDbType == SqlDbType.Int &&
                    (int)parameters[0].Value == purchaseOrderId)))
                .ReturnsAsync(new List<PurchaseOrderForPageReadModel>());

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);

            _mockRepository.Verify(x => x.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithNullRepositoryResult_ReturnsUnsuccessfulResponse()
        {
            // Arrange
            var purchaseOrderId = 456;
            var query = new GetPurchaseOrderForPageQuery(purchaseOrderId);

            _mockRepository.Setup(x => x.SqlQueryRawAsync(
                It.Is<string>(sql => sql.Contains(StoredProcedures.Select_PurchaseOrder_for_Page) && sql.Contains("@PurchaseOrderId")),
                It.Is<SqlParameter[]>(parameters =>
                    parameters.Length == 1 &&
                    parameters[0].ParameterName == "@PurchaseOrderId" &&
                    parameters[0].SqlDbType == SqlDbType.Int &&
                    (int)parameters[0].Value == purchaseOrderId)))
                .ReturnsAsync((List<PurchaseOrderForPageReadModel>)null!);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);

            _mockRepository.Verify(x => x.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()), Times.Once);
        }
    }
}