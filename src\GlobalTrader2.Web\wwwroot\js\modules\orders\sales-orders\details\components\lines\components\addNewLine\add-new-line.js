﻿import { StepperComponent } from "../../../../../../../../components/stepper/stepper.component.js?v=#{BuildVersion}#";
import { AddNewLineStep2Manager } from "./add-new-line-step-2.js?v=#{BuildVersion}#";
import { AddNewLineStep3Manager } from "./add-new-line-step-3.js?v=#{BuildVersion}#";
import { AddNewLineStep4Manager } from "./add-new-line-step-4.js?v=#{BuildVersion}#";
import { AddLineDetailModel } from '../../models/add-line-detail.model.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';

export class AddNewLineManager {
    constructor({ globalClientNo, soGeneralInfo, salesOrderMainInfoDto }) {
        this.stepper = null;
        this.selectedSource = "";
        this.addNewLineStep2Manager = null;
        this.addNewLineStep3Manager = null;
        this.addNewLineStep4Manager = null;
        this.dataForBinding = null;
        this.globalClientNo = globalClientNo;
        this.$dialog = $("#add-lines-dialog");
        this.setDefaultSelectedSource();
        this._soGeneralInfo = soGeneralInfo;
        this._salesOrderMainInfoDto = salesOrderMainInfoDto;
        this._creditData = null;
    }

    initialize() {
        this.setupDialog();
        this.initStepper();
        this.eventRegister();
    }

    setDefaultSelectedSource() {
        const $firstSource = $('#form-add-so-line-step1 input[type="radio"]').first();
        if ($firstSource.length && $firstSource.val() !== null && $firstSource.val() !== undefined) {
            $firstSource.prop('checked', true);
            this.selectedSource = $firstSource.val();
        }
    }

    initStepper() {
        this.stepper = new StepperComponent(document.getElementById('add-lines-stepper'), [
            { title: `Select Source`, clickToMove: true },
            { title: `Select Item`, clickToMove: true },
            { title: `Edit Details`, clickToMove: false },
            { title: `Lot Details`, clickToMove: false },
        ]);
        this.stepper.on("stepChange.ms", ({ step }) => {
            if (window.currentXhr && window.currentXhr.status != 0) {
                window.currentXhr.abort();
                window.currentXhr = undefined;
            }
            this.enableSaveButton(this.stepper.currentStep);
            $('#add-lines-stepper .step-arrow[data-step="2"], #add-lines-stepper .step-arrow[data-step="3"]').removeClass('pe-none');
            if (this.selectedSource == FromSourceTypeConstant.NEW) {
                if (step == 1 || step == 3) {
                    $('#add-lines-stepper .step-arrow[data-step="2"]').removeClass('completed');
                    $('#add-lines-stepper .step-arrow[data-step="2"]').addClass('pe-none');
                }
            }
            else if (this.selectedSource == FromSourceTypeConstant.NEWLOT) {
                if (step == 1 || step == 4) {
                    $('#add-lines-stepper .step-arrow[data-step="2"], #add-lines-stepper .step-arrow[data-step="3"]').removeClass('completed');
                    $('#add-lines-stepper .step-arrow[data-step="2"], #add-lines-stepper .step-arrow[data-step="3"]').addClass('pe-none');
                }
            }
        });
        this.stepper.trigger("stepChange.ms", { step: this.stepper.currentStep });
    }

    enableSaveButton(currentStep) {
        $("#save-so-line-btn").prop('disabled', currentStep == 1 || currentStep == 2);
    }

    async fillDataStep3(sourceSelected, sourceItemId) {
        let getSourceDataPromise = null;
        switch (sourceSelected) {
            case FromSourceTypeConstant.CUSREQ:
                getSourceDataPromise = this.GetRequirementLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.SOURCINGRESULT:
                getSourceDataPromise = this.GetSourcingResultForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.QUOTE:
                getSourceDataPromise = this.GetQuoteLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.SO:
                getSourceDataPromise = this.GetSalesOrderLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.STOCK:
                getSourceDataPromise = this.GetStockLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.SERVICE:
                getSourceDataPromise = this.GetServiceLineForNew(sourceItemId);
                break;
            default:
                break;
        }
        this.addNewLineStep3Manager.showForm(this._salesOrderMainInfoDto, getSourceDataPromise, sourceSelected);
    }

    async GetRequirementLineForNew(sourceItemId) {
        const requirementLine = await GlobalTrader.ApiClient.getAsync(`/orders/customer-requirements/part-detail/${sourceItemId}`);
        this.dataForBinding = new AddLineDetailModel();
        this.dataForBinding.Part = requirementLine.data.part;
        this.dataForBinding.SourceSelected = FromSourceTypeConstant.CUSREQ;
        return this.dataForBinding;
    }

    async GetSourcingResultForNew(sourceItemId) {
        const sourcingResult = await GlobalTrader.ApiClient.getAsync(`/orders/customer-requirements/part-detail/sourcing-results/${sourceItemId}`);
        this.dataForBinding = new AddLineDetailModel();
        this.dataForBinding.Part = sourcingResult.data.part;
        this.dataForBinding.SourceSelected = FromSourceTypeConstant.SOURCINGRESULT;
        return this.dataForBinding;
    }

    async GetQuoteLineForNew(sourceItemId) {
        const quoteLineDetail = await GlobalTrader.ApiClient.getAsync(`/orders/quotes/quote-line/${sourceItemId}/for-new`);
        this.dataForBinding = new AddLineDetailModel();
        this.dataForBinding.Part = quoteLineDetail.data.part;
        this.dataForBinding.SourceSelected = FromSourceTypeConstant.QUOTE;
        return this.dataForBinding;
    }

    async GetSalesOrderLineForNew(sourceItemId) {

        const salesOrderLineDetail = await GlobalTrader.ApiClient.getAsync(`orders/sales-order/so-lines/${sourceItemId}/detail`);
        this.dataForBinding = new AddLineDetailModel();
        this.dataForBinding.Part = salesOrderLineDetail.data.part;
        this.dataForBinding.SourceSelected = FromSourceTypeConstant.SO;
        return this.dataForBinding;
    }

    async GetStockLineForNew(sourceItemId) {

        const stockDetail = await GlobalTrader.ApiClient.getAsync(`stocks/${sourceItemId}/detail`);
        this.dataForBinding = new AddLineDetailModel();
        this.dataForBinding.Part = stockDetail.data.part;
        this.dataForBinding.SourceSelected = FromSourceTypeConstant.STOCK;
        return this.dataForBinding;
    }
    async GetServiceLineForNew(sourceItemId) {
        const serviceDetail = await GlobalTrader.ApiClient.getAsync(`services/${sourceItemId}/detail`);
        this.dataForBinding = new AddLineDetailModel();
        this.dataForBinding.SourceSelected = FromSourceTypeConstant.SERVICE;
        return this.dataForBinding;
    }

    setCreditData(creditData) {
        this._creditData = creditData;
    }

    eventRegister() {
        $('input:radio[name=addSoLineSource]').click((event) => {
            this.selectedSource = event.target.value;
        });
    }
    setupDialog() {
        this.$dialog.dialog({
            maxHeight: $(window).height(),
            width: "80vw",
            close: () => {
                if (this.addNewLineStep3Manager != null) {
                    this.addNewLineStep3Manager.onFormClose();
                }
                this.selectedSource = this.setDefaultSelectedSource();
                this.stepper.updateStepper(1);
                this.$dialog.dialog("close");
            },
            buttons: [
                {
                    text: window.localizedStrings.save,
                    id: 'save-so-line-btn',
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/save.svg" alt="${window.localizedStrings.save}">${window.localizedStrings.save}`,
                    click: async () => {
                        if (this.addNewLineStep3Manager?.submitForm()) {
                            this.$dialog.find(".form-error-summary").hide();
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
                            this.$dialog.dialog("setLoading", true);

                            this.$dialog.dialog("setLoading", false);
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);

                            this.$dialog.dialog("close");
                            showToast('success', window.localizedStrings.saveChangedMessage);

                            if (this.successCallback) this.successCallback();
                        }
                        else {
                            this.$dialog.find(".form-error-summary").show();
                        }
                    }
                },
                {
                    text: window.localizedStrings.cancel,
                    class: 'btn btn-danger fw-normal',
                    html: `<img src="/img/icons/slash.svg" alt="${window.localizedStrings.cancel}">${window.localizedStrings.cancel}`,
                    click: function () {
                        $(this).dialog("close");
                    },
                },
                {
                    text: window.localizedStrings.continue,
                    class: 'btn btn-primary',
                    html: `<img src="/img/icons/arrow-right.svg" alt="Continue icon"/>${window.localizedStrings.continue}`,
                    click: () => {
                        this.continueClicked();
                    },
                },
            ]
        });
    }

    continueClicked() {
        switch (this.selectedSource) {
            case FromSourceTypeConstant.NEW:
                this.stepper.updateStepper(3);
                if (this.addNewLineStep3Manager == null) {
                    this.addNewLineStep3Manager = new AddNewLineStep3Manager(this._soGeneralInfo);
                    this.addNewLineStep3Manager.initialize();
                }
                this.dataForBinding = new AddLineDetailModel();
                this.dataForBinding.SourceSelected = FromSourceTypeConstant.NEW;
                this.addNewLineStep3Manager.showForm(this._salesOrderMainInfoDto, null, FromSourceTypeConstant.NEW);
                break;
            case FromSourceTypeConstant.CUSREQ:
            case FromSourceTypeConstant.QUOTE:
            case FromSourceTypeConstant.SO:
            case FromSourceTypeConstant.STOCK:
            case FromSourceTypeConstant.SERVICE:
                this.stepper.updateStepper(2);
                if (this.addNewLineStep2Manager == null) {
                    this.addNewLineStep2Manager = new AddNewLineStep2Manager({ globalClientNo: this.globalClientNo });
                    this.addNewLineStep2Manager.initialize();
                    this.addNewLineStep2Manager.loadSection(this.selectedSource);
                    this.addNewLineStep2Manager.on('continue.clicked', (data) => {
                        this.stepper.onNextStep();
                        if (this.addNewLineStep3Manager == null) {
                            this.addNewLineStep3Manager = new AddNewLineStep3Manager(this._soGeneralInfo);
                            this.addNewLineStep3Manager.initialize();
                        }
                        this.fillDataStep3(data.sourceSelected, data.sourceItemId);
                    });
                }
                else {
                    this.addNewLineStep2Manager.loadSection(this.selectedSource);
                }
                break;
            case FromSourceTypeConstant.NEWLOT:
                this.stepper.updateStepper(4);
                if (this.addNewLineStep4Manager == null) {
                    this.addNewLineStep4Manager = new AddNewLineStep4Manager();
                    this.addNewLineStep4Manager.initialize();
                }
                break;
        }
    }

    openDialog() {
        this.$dialog.dialog("open");
        this.handleDialogOpen();
    }

    async handleDialogOpen() {
        this.$dialog.dialog("setLoading", true);
        this.$dialog.dialog("setLoading", false);
    }
}