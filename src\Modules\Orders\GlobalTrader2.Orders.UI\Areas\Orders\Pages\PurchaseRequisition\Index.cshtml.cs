using GlobalTrader2.Dto.DataListNugget;
using GlobalTrader2.Dto.PurchaseRequisition;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.UserAccount.UseCases.FilterState.Queries.GetFilterStates;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseRequisition
{
    [SectionAuthorize(SecurityFunction.OrdersSection_View)]
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly IMediator _mediator;
        public ViewLevelList CurrentTab { get; set; } = 0;

        public DataListNuggetState<PurchaseRequisitionFilter> DataListNuggetState { get; set; } = new DataListNuggetState<PurchaseRequisitionFilter>();

        public IndexModel(SecurityManager securityManager, IMediator mediator, SessionManager sessionManager) : base(securityManager)
        {
            _sessionManager = sessionManager;
            AddBreadCrumbs();
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_PurchaseRequisitionBrowse;
            _mediator = mediator;
        }
        public async Task<IActionResult> OnGet()
        {
            var listTabs = await GetVisibleTabSecurityList(SecurityFunction.Orders_PurchaseRequisitions_View);

            if (listTabs == null || !listTabs.Contains((int)ViewLevelList.My)) return Redirect(V2Paths.NotFound);

            var defaultPage = _sessionManager.DefaultListPageView;
            CurrentTab = !listTabs.Exists(t => t == (int)defaultPage) ? ViewLevelList.My : defaultPage;

            SetupPermissions();
            VisibleTab = Enum.GetValues(typeof(ViewLevelList))
               .Cast<ViewLevelList>()
               .Where(e => listTabs.Contains((int)e))
               .ToList();
            int loginId = _sessionManager.GetInt32(SessionKey.LoginID).GetValueOrDefault();
            DataListNuggetState = await GetDataListNuggetState((int)DataListNuggets.PurchaseRequisitions, loginId);
            return Page();
        }

        private async Task<DataListNuggetState<PurchaseRequisitionFilter>> GetDataListNuggetState(int intDataListNuggetID, int userId)
        {
            var dataListNuggetState = await _mediator.Send(new GetFilterStatesQuery
            {
                DataListNuggetNo = intDataListNuggetID,
                LoginNo = userId,
            });

            if (dataListNuggetState.Success && dataListNuggetState.Data != null)
            {
                return DataListNuggetState<PurchaseRequisitionFilter>.Deserialize(dataListNuggetState.Data.StateText);
            }

            return new DataListNuggetState<PurchaseRequisitionFilter>();
        }
        private void SetupPermissions()
        {
            if (_securityManager == null) return;
        }
        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.PurchaseRequisitions);
            BreadCrumbMenu.Add(BreadCrumbHelper.Order_MenuNew(_securityManager!));
        }
    }
}
