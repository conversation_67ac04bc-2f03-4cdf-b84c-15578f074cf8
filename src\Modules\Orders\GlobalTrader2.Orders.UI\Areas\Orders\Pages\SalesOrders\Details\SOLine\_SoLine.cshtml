@using GlobalTrader2.Dto.SalesOrderLine
@using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.CollapsibleFieldSetLite
@using GlobalTrader2.SharedUI.Interfaces

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IWebResourceManager WebResourceManager
@inject IStringLocalizer<SharedUI.Status> _statusLocalizer;



@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details.Index

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
}

<div id="lines-box" class="@sectionBoxClasses mb-3">
    <h3 class="@headerClasses">
        <span class="section-box-title">
            @_localizer["Lines"]
        </span>
        <span class="section-box-button-group">
            @if (!Model.LinesSectionViewModel.IsReadOnly)
            {
                <span class="d-flex flex-wrap gap-2">
                    @if (Model.LinesSectionViewModel.CanAdd)
                    {
                        <button class="btn btn-primary" id="lines-add-btn" disabled>
                            <img src="~/img/icons/plus.svg" alt="@_commonLocalizer["Add"]" />
                            <span>@_commonLocalizer["Add"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanEdit)
                    {
                        <button class="btn btn-primary" id="lines-edit-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_commonLocalizer["Edit"]" />
                            <span>@_commonLocalizer["Edit"]</span>
                        </button>
                        @await Html.PartialAsync("_EditSoLine.cshtml")
                    }
                    @if (Model.LinesSectionViewModel.CanPost)
                    {
                        <button class="btn btn-primary" id="lines-post-btn" disabled>
                            <img src="~/img/icons/clipboard-check.svg" alt="@_commonLocalizer["Post"]" />
                            <span>@_commonLocalizer["Post"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanPost)
                    {
                        <button class="btn btn-primary" id="lines-post-all-btn" disabled>
                            <img src="~/img/icons/clipboard-check.svg" alt="@_commonLocalizer["Post All"]" />
                            <span>@_commonLocalizer["Post All"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanUnpost)
                    {
                        <button class="btn btn-primary" id="lines-unpost-btn" disabled>
                            <img src="~/img/icons/clipboard-cross.svg" alt="@_commonLocalizer["Unpost"]" />
                            <span>@_commonLocalizer["Unpost"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanUnpost)
                    {
                        <button class="btn btn-primary" id="lines-unpost-all-btn" disabled>
                            <img src="~/img/icons/clipboard-cross.svg" alt="@_commonLocalizer["Unpost All"]" />
                            <span>@_commonLocalizer["Unpost All"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanDelete)
                    {
                        <button class="btn btn-danger" id="lines-delete-btn" disabled>
                            <img src="~/img/icons/trash-can.svg" alt="@_commonLocalizer["Delete"]" />
                            <span>@_commonLocalizer["Delete"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanAllocate)
                    {
                        <button class="btn btn-primary" id="lines-allocate-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_commonLocalizer["Allocate"]" />
                            <span>@_commonLocalizer["Allocate"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanClose)
                    {
                        <button class="btn btn-danger" id="lines-close-btn" disabled>
                            <img src="~/img/icons/slash.svg" alt="@_commonLocalizer["Close"]" />
                            <span>@_commonLocalizer["Close"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanCreateIpo)
                    {
                        <button class="btn btn-primary" id="lines-create-ipo-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["Create IPO"]" />
                            <span>@_localizer["Create IPO"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanCloneThisLine)
                    {
                        <button class="btn btn-primary" id="lines-clone-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["Clone this line"]" />
                            <span>@_localizer["Clone this line"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanConfirm)
                    {
                        <button class="btn btn-primary" id="lines-confirm-btn" disabled>
                            <img src="~/img/icons/check-circle.svg" alt="@_commonLocalizer["Confirm"]" />
                            <span>@_commonLocalizer["Confirm"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanConfirmAll)
                    {
                        <button class="btn btn-primary" id="lines-confirm-all-btn" disabled>
                            <img src="~/img/icons/check-circle.svg" alt="@_commonLocalizer["Confirm All"]" />
                            <span>@_commonLocalizer["Confirm All"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanEditAll)
                    {
                        <button class="btn btn-primary" id="lines-edit-all-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["Edit-All"]" />
                            <span>@_localizer["Edit-All"]</span>
                        </button>
                    }
                    @if (Model.LinesSectionViewModel.CanEccnLog)
                    {
                        <button class="btn btn-primary" id="lines-eccn-log-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["ECCN Log"]" />
                            <span>@_localizer["ECCN Log"]</span>
                        </button>
                    }
                </span>
            }
        </span>
    </h3>
    <div class="@contentClasses" style="display: none">
        <div id="lines-wrapper">
            <div id="lines-all-message" class="nugget-message d-none"></div>
            <div id="lines-open-message" class="nugget-message d-none"></div>
            <div id="lines-closed-message" class="nugget-message d-none"></div>
            <div class="d-flex justify-content-between align-items-start border-bottom">
                <div id="nav-tabs-wrapper" role="tablist">
                    <ul class="nav nav-tabs border-0 justify-content-start" id="lines-tabs">
                        <li class="nav-item">
                            <button id="lines-all-tab" class="nav-link active" data-bs-toggle="tab" data-bs-target="#lines-all"
                                type="button" role="tab" aria-controls="all" aria-selected="true"
                                data-view-level="@((int)LineSectionTab.all)">
                                @_commonLocalizer["All"]
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="lines-open-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#lines-open"
                                type="button" role="tab" aria-controls="open" aria-selected="false"
                                data-view-level="@((int)LineSectionTab.open)">
                                @_commonLocalizer["Open"]
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="lines-closed-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#lines-closed"
                                type="button" role="tab" aria-controls="closed" aria-selected="false"
                                data-view-level="@((int)LineSectionTab.closed)">
                                @_commonLocalizer["Closed"]
                            </button>
                        </li>
                    </ul>
                </div>
            </div>


            <div class="tab-content">
                <div class="tab-pane fade active show" id="lines-all" role="tabpanel" aria-labelledby="lines-all-tab">
                    <div class="table-container">
                        <div id="lines-all-wrapper" class="position-relative">
                            <table id="lines-all-table" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                            <div class="d-flex flex-row-reverse gap-3 p-2" style="background-color: #EEEEEE;">
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Total"]</span>
                                    
                                    <span id="lines-all-total" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Tax"]</span>
                                    
                                    <span id="lines-all-tax" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Freight"]</span>
                                    
                                    <span id="lines-all-freight" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["SubTotal"]</span>
                                    
                                    <span id="lines-all-subtotal" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                            </div>
                            @await Html.PartialAsync("SOLine/_SoLineDetail", LineSectionTab.all.ToString())
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="lines-open" role="tabpanel" aria-labelledby="lines-open-tab">
                    <div class="table-container">
                        <div id="lines-open-wrapper" class="position-relative">
                            <table id="lines-open-table" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                            <div class="d-flex flex-row-reverse gap-3 p-2" style="background-color: #EEEEEE;">
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Total"]</span>
                                    
                                    <span id="lines-open-total" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Tax"]</span>
                                    
                                    <span id="lines-open-tax" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Freight"]</span>
                                    
                                    <span id="lines-open-freight" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["SubTotal"]</span>
                                    
                                    <span id="lines-open-subtotal" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                            </div>
                            @await Html.PartialAsync("SOLine/_SoLineDetail", LineSectionTab.open.ToString())
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="lines-closed" role="tabpanel" aria-labelledby="lines-closed-tab">
                    <div class="table-container">
                        <div id="lines-closed-wrapper" class="position-relative">
                            <table id="lines-closed-table" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                            <div class="d-flex flex-row-reverse gap-3 p-2" style="background-color: #EEEEEE;">
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Total"]</span>
                                    
                                    <span id="lines-closed-total" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Tax"]</span>
                                    
                                    <span id="lines-closed-tax" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Freight"]</span>
                                    
                                    <span id="lines-closed-freight" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["SubTotal"]</span>
                                    
                                    <span id="lines-closed-subtotal" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                            </div>
                            @await Html.PartialAsync("SOLine/_SoLineDetail", LineSectionTab.closed.ToString())
                        </div>
                    </div>
                </div>

                @await Component.InvokeAsync(nameof(CollapsibleFieldSetLite), Model.AllocationsForLineDetailsViewModel)
            </div>
        </div>
    </div>
</div>

<div>
    @await Html.PartialAsync("_CloseLine.cshtml",Model.LinesSectionViewModel)
    @await Html.PartialAsync("_ConfirmLine.cshtml",Model.LinesSectionViewModel)
    @await Html.PartialAsync("_PostLine.cshtml",Model.LinesSectionViewModel)
    @await Html.PartialAsync("_AddSoLine.cshtml", Model.LinesSectionViewModel)
</div>

<script>
    const linesResource = {
        lineNo: '@_localizer["Line No"]',
        partNo: '@_localizer["Part No"]',
        customerPart: '@_localizer["Customer Part"]',
        mfr: '@_localizer["Mfr"]',
        dc: '@_localizer["DC"]',
        product: '@_localizer["Product"]',
        package: '@_localizer["Package"]',
        ordered: '@_localizer["Qty Ordered"]',
        shipped: '@_localizer["Qty Shipped"]',
        allocated: '@_localizer["Qty Allocated"]',
        backOrder: '@_localizer["Qty BackOrder"]',
        unitPrice: '@_localizer["Unit Price"]',
        total: '@_localizer["Total"]',
        tax: '@_localizer["Tax"]',
        dateConfirmed: '@_localizer["Date Confirmed"]',
        asRequired: '@_localizer["AS6081 Required?"]',
        reason: '@_localizer["Reason"]',
        promiseDate: '@_localizer["Promise Date"]',
        by: '@_localizer["By"]',
        date: '@_localizer["Date"]',
    }

    const salesOrderInfo = @Json.Serialize( new {
        Status = Model.SalesOrderGeneralInfo.Status,
        AutoApprove= Model.SalesOrderGeneralInfo.AutoApproveSO,
        Authorised = _statusLocalizer[((Core.Enums.SalesOrderStatus)8).ToString()],
        Complete = _statusLocalizer[((Core.Enums.SalesOrderStatus)10).ToString()]


    })

</script>

