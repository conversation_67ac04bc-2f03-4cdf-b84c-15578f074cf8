﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.SendApprovalRequest
{
    public class SendApprovalRequestCommand : IRequest<BaseResponse<bool>>
    {
        [Required]
        public required int LoginId { get; set; }

        [Required]
        public required int ClientId { get; set; }
        [Required]
        public required int SalesOrderId { get; set; }
        [Required]
        public required string Subject { get; set; }
        [Required]
        public required string Message { get; set; }
        [Required]
        public required List<int> ApproverIds { get; set; }
        [Required]
        public bool IsNotifySO { get; set; }
        [Required]
        public required string Uri { get; set; }
        [Required]
        public required string V1Uri { get; set; }
        [Required]
        public required string LoginFullName { get; set; }
        [Required]
        public required string BaseDirectory { get; set; }
    }
}
