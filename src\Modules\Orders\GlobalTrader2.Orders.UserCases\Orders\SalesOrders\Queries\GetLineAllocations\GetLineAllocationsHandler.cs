﻿using GlobalTrader2.Dto.Allocation;

namespace GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetLineAllocations
{
    public class GetLineAllocationsHandler : IRequestHandler<GetLineAllocationsQuery, BaseResponse<IEnumerable<AllocationDetailsDto>>>
    {
        private readonly IBaseRepository<AllocationDetailsReadModel> _allocationRepository;
        private readonly IMapper _mapper;

        public GetLineAllocationsHandler(IBaseRepository<AllocationDetailsReadModel> allocationRepository, IMapper mapper)
        {
            _allocationRepository = allocationRepository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<IEnumerable<AllocationDetailsDto>>> Handle(GetLineAllocationsQuery request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>
            {
                 new SqlParameter("@SalesOrderLineId", SqlDbType.Int){ Value = request.salesOrderLineId},
            };

            var result = await _allocationRepository.SqlQueryRawAsync($"{StoredProcedures.SelectAll_Allocation_for_SalesOrderLine} @SalesOrderLineId", parameters.ToArray());
            return new BaseResponse<IEnumerable<AllocationDetailsDto>>
            {
                Success = true,
                Data = _mapper.Map<IEnumerable<AllocationDetailsDto>>(result)
            };
        }
    }
}
