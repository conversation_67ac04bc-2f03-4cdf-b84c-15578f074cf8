﻿using GlobalTrader2.Dto.SalesOrderLine;
using Microsoft.IdentityModel.Tokens;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPOStock
{
    public class GetSalesOrderLineReportPoStockHandler : IRequestHandler<GetSalesOrderLineReportPoStockQuery, BaseResponse<IReadOnlyList<SalesOrderLineReportPoStockDto>>>
    {
        private readonly IBaseRepository<SalesOrderLineReportPoStockReadModel> _salesOrderLinePoStockRepository;
        private readonly IMapper _mapper;
        public GetSalesOrderLineReportPoStockHandler(IBaseRepository<SalesOrderLineReportPoStockReadModel> salesOrderLinePoStockRepository, IMapper mapper)
        {
            _salesOrderLinePoStockRepository = salesOrderLinePoStockRepository;
            _mapper = mapper;
        }
        public async Task<BaseResponse<IReadOnlyList<SalesOrderLineReportPoStockDto>>> Handle(GetSalesOrderLineReportPoStockQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IReadOnlyList<SalesOrderLineReportPoStockDto>>();

            var paramters = new List<SqlParameter>()
            { new SqlParameter("@SalesOrderLineId", SqlDbType.Int) { Value = request.SalesOrderLineId } };
            var queryResult = await _salesOrderLinePoStockRepository.SqlQueryRawAsync
                ($"{StoredProcedures.Get_All_SalesOrderLine_ReportPOStock} @SalesOrderLineId", paramters.ToArray());
            if (queryResult.Any())
            {
                response.Data = _mapper.Map<IReadOnlyList<SalesOrderLineReportPoStockDto>>(queryResult);
            }
            response.Success = true;
            return response;
        }
    }
}
