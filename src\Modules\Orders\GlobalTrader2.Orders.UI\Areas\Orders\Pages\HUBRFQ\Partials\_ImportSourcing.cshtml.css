﻿.correction-title {
    font-weight: bold;
    background: var(--btn-primary-color);
    padding: 5px;
}

.correction-inputs {
    padding: 5px 12px;
}

::deep .table-border td {
    border-width: 1px;
}

::deep table.dataTable > tbody > tr > td {
    padding: 5px;
}

::deep .dirty-cell {
    background-image: linear-gradient(135deg, red 5px, transparent 5px);
}

::deep .rounded-circle {
    margin-left: 0px !important;
}

#hubrfq-import-sourcing-raw-data-table ::deep input.form-control[type="text"] {
    padding: 0.175rem 0rem;
}

::deep .form-control.text-danger:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, .25) !important;
}

/* Fix colReorder ghost positioning */
body .dtcr-cloned {
    z-index: 2000 !important;
    pointer-events: none;
    position: absolute !important;
    background: #fff;
    opacity: 0.95;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

/* Prevent helper from going behind the dialog */
::deep .ui-dialog {
    overflow: visible !important;
}

::deep .column-header-icon {
    width: 12px;
    height: 12px;
}

.cursor-pointer {
    cursor: pointer;
}

#hubrfq-import-sourcing-raw-data-table ::deep td {
    cursor: auto;
}

#hubrfq-import-sourcing-raw-data-table ::deep td.cursor-default {
    cursor: default !important;
}

.btn-cursor-wrapper:has(button:disabled):hover {
    cursor: url('/img/icons/slash-red.svg'), not-allowed;
}