﻿import { SalesOrdersTableFilterComponent } from '../sales-orders/components/sales-orders-table-filter-component.js?v=#{BuildVersion}#';
import { FieldType } from '../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { TextFilterHelper } from "../../../helper/text-filter-helper.js?v=#{BuildVersion}#";
import { DebounceHelper } from '../../../helper/debounce-helper.js?v=#{BuildVersion}#'
import { NumberType } from "../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#";

$(async () => {
    const salesOrdersManager = new SalesOrdersManager();
    await salesOrdersManager.initialize();
});

class SalesOrdersManager {
    constructor() {
        this.isSalespersonFirstLoad = true;
        this.$salesOrdersSectionBox = $('#sales-orders-box');
        this.$selectAllButton = $('#select-all-button');
        this.$lockUnlockButton = $('#lock-unlock-button');
        this.$warningSection = $('#warning-section');
        this.filterStates = JSON.parse($('#filter-section-wrapper').attr("data-states"));
        this.salesOrdersConfig = JSON.parse($('#filter-section-wrapper').attr("data-config"));
        this.clientFilterState = {
            salespersonEndpoint: 'setup/security-settings/security-users/users-client',
            countriesEndpoint: 'countries/dropdown-countries',
            params: {
                clientNo: null
            }
        };

        this.filterInputs = [
            {
                fieldType: FieldType.NUMBER,
                label: 'Sales Order',
                name: 'SONo',
                id: 'SONo',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true,
                },
                extraPros: {
                    numberType: NumberType.INT
                },
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Part No',
                name: 'Part',
                id: 'Part',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: 'Recent Only?',
                name: 'RecentOnly',
                id: 'RecentOnly',
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: 'Include Closed',
                name: 'IncludeClosed',
                id: 'IncludeClosed',
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Company',
                name: 'CMName',
                id: 'CMName',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Contact Name',
                name: 'Contact',
                id: 'Contact',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'Client Name',
                name: 'ClientName',
                id: 'ClientName',
                value: '',
                options: {
                    serverside: false,
                    endpoint: '/user-account/clients/active',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    params: { isIncludeClient: false },
                    placeholderValue: "",
                    onSelected: (data, selectedClient) => {
                        if (!data || !this.salesOrdersConfig.isGlobalLogin || this.isSalespersonFirstLoad) {
                            this.isSalespersonFirstLoad = false;
                            return;
                        };
                        this.clientFilterState.params.clientNo = selectedClient;
                        this.reloadSalesPersonDropdown();
                        this.reloadCountryDropdown();
                        this.UnCheckedInputFilterByName("Salesman");
                        this.UnCheckedInputFilterByName("Country");
                    },
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'Salesperson',
                name: 'Salesman',
                id: 'Salesman',
                value: '',
                options: {
                    serverside: false,
                    endpoint: this.clientFilterState.salespersonEndpoint,
                    valueKey: 'loginId',
                    textKey: 'employeeName',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'Country',
                name: 'Country',
                id: 'Country',
                value: '',
                options: {
                    serverside: false,
                    endpoint: this.clientFilterState.countriesEndpoint,
                    valueKey: 'countryId',
                    textKey: 'countryName',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Customer PO',
                name: 'CustPO',
                id: 'CustPO',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Contract No',
                name: 'ContractNo',
                id: 'ContractNo',
                value: '',
                attributes: {
                    "maxlength": 100
                },
                locatedInContainerByClass: 'filter-column-2'
            },

            {
                fieldType: FieldType.SELECT,
                label: 'SOs sent to customer only',
                name: 'IncludeOrderSent',
                id: 'IncludeOrderSent',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/so-sent-to-customer',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                    placeholder: null
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'AS6081 testing required?',
                name: 'AS6081',
                id: 'AS6081',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/counterfeit-electronic-parts',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Ordered From',
                name: 'DateOrderedFrom',
                id: 'DateOrderedFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-3'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Ordered To',
                name: 'DateOrderedTo',
                id: 'DateOrderedTo',
                value: '',
                locatedInContainerByClass: 'filter-column-3'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Promised From',
                name: 'DatePromisedFrom',
                id: 'DatePromisedFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-3'
            },
            {
                fieldType: FieldType.DATE,
                label: 'Date Promised To',
                name: 'DatePromisedTo',
                id: 'DatePromisedTo',
                value: '',
                locatedInContainerByClass: 'filter-column-3'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'Status',
                name: 'SalesOrderStatus',
                id: 'SalesOrderStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/sales-orders-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-3'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'Checked?',
                name: 'SOCheckedStatus',
                id: 'SOCheckedStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/so-checked-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-3'
            }
        ];
        this.pageSize = $('#salesOrdersTbl').data('default-page-size') || 10;
        this.defaultPageIndex = $('#salesOrdersTbl').data('default-page-index');
        this.tableFilter = null;
        this.$salesOrdersTable = null;
        this.isLockFilter = this.salesOrdersConfig.initSaveDataListState;
        this.currentXhr = null;
        this.currentTab = this.salesOrdersConfig.initViewLevel;
        
        this.sortIndex = this.salesOrdersConfig.defaultSortIndex;
        this.sortDirection = this.salesOrdersConfig.defaultSortDirection == 0 ? window.constants.sortDESC : this.salesOrdersConfig.defaultSortDirection;
        this._$addToDoDialog = $("#add-to-do-dialog");
        this.$exportExcelButton = $('#export-sale-orders')
    }

    async initialize() {
        await this.initTableFilter();
        this.initDataTable()
        this.setupSalesOrdersSectionBox();
        this._onSaveAddTaskComplete();
    }

    setupSalesOrdersSectionBox() {
        this.$salesOrdersSectionBox.section_box({
            loading: true,
            loadingContentId: 'salesOrdersTbl_wrapper',
            onRefreshClick: async (event, ui) => {
                this.refreshDatatable();
            }
        });
    }

    async initTableFilter() {
        this.tableFilter = new SalesOrdersTableFilterComponent('#filter-section-wrapper', 'Filter Results', {
            inputConfigs: this.filterInputs
        });

        await this.tableFilter.init();
        this.hideClientName(!this.salesOrdersConfig.isGlobalLogin && !this.salesOrdersConfig.isGSA);
        if (this.currentTab == 0) { // my tab
            this.hideSalesmanFilter(this.currentTab);
        }
        this.tableFilter.on('applied.mtf', () => {
            this.$salesOrdersTable.rows().deselect();
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$salesOrdersTable, true);
        })

        this.tableFilter.on('cancel.mtf', () => {
            if (this.currentXhr) {
                this.currentXhr.abort();
                this.$warningSection.show();
                $('#sales-orders-box').section_box('option', 'loading', false);
                this.tableFilter.toggleApplyCancelButtons(true);
                this.showFilterButtons(true);
            }
        })

        this.tableFilter.on('off.mtf', () => {
            this.$salesOrdersTable.rows().deselect();
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$salesOrdersTable);
        });

        this.tableFilter.on('reset.mtf', () => {
            history.pushState({ additionalInformation: 'Reset filter' }, 'Reset filter', `/Orders/SalesOrders`);
            const recentOnlyInput = this.tableFilter.getInputElementByName("RecentOnly");
            recentOnlyInput.setRequiredCheckbox(true);
            recentOnlyInput.setValue(true);
            recentOnlyInput.syncInputState();
            recentOnlyInput.triggerControlChanged();
        });

        Object.values(this.filterStates).forEach(input => {
            if (input.isOn && input.isShown && input.value && input.name) {
                const currentInput = this.tableFilter.getInputElementByName(input.name);

                switch (input.fieldType) {
                    case FieldType.TEXT:
                        currentInput.setValue(TextFilterHelper.getFilterValue(input.searchType, input.value));
                        break;
                    case FieldType.STAR_RATING:
                    case FieldType.NUMBER:
                        currentInput.setState(input);
                        break;

                    default:
                        currentInput.setValue(input.value);
                        break;
                }

                currentInput.setRequiredCheckbox(true);
                currentInput.syncInputState();
                currentInput.triggerControlChanged();
            }
        });
        this.addClientNameFilterEvent();
    }

    async refreshDatatable() {
        GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$salesOrdersTable);
    }

    initDataTable() {
        $('#sales-orders-box-content').hide(); // Default hide filter on init
        let tableColumnsDefine = [
            {
                name: 'salesOrderLineId',
                data: 'salesOrderLineId',
                title: 'salesOrderLineId',
                visible: false
            },
            {
                name: 'salesOrderNo',
                data: 'salesOrderNo',
                title: 'No',
                type: 'string',
                className: 'text-wrap',
                render: (data, type, row) => {
                    return `
                                <div class="m-0" style="min-height: 15px;">
                                    <a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_SalesOrders(row.salesOrderNo)}">
                                        ${row.salesOrderNumber} 
                                    </a> 
                                </div>
                            `
                }
            },
            {
                data: (row) => (
                    {
                        partNo: row.part,
                        customerPO: row.customerPO,
                    }
                ),
                name: 'partNo_customerPO',
                title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Customer PO No`,
                type: 'string',
                className: 'text-wrap text-break',
                render: (data) => {
                    let escapedPartNo = "";
                    let escapedCustomerPO = "";

                    if (data.partNo) {
                        const partNoText = GlobalTrader.StringHelper.setCleanTextValue(data.partNo);
                        escapedPartNo = DataTable.render.text().display(partNoText);
                    }
                    if (data.customerPO) {
                        const customerPOText = GlobalTrader.StringHelper.setCleanTextValue(data.customerPO);
                        escapedCustomerPO = DataTable.render.text().display(customerPOText);
                    }

                    return `<p class="m-0" style="min-height: 15px;">${escapedPartNo}</p><p class="m-0" style="min-height: 15px;">${escapedCustomerPO}</p>`;
                }
            },
            {
                data: (row) => (
                    {
                        quantityOrdered: row.quantity,
                        quantityShipped: row.quantityShipped,
                        quantityInStock: row.quantityInStock,
                    }
                ),
                name: 'qtyOrdered_qtyShipped_qtyInStock',
                title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Qty Ordered</div><div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Qty Shipped</div>Qty In Stock`,
                type: 'string',
                className: 'text-wrap text-break',
                render: (data) => {
                    return `<p class="m-0" style="min-height: 15px;">${data.quantityOrdered ?? 0}</p><p class="m-0" style="min-height: 15px;">${data.quantityShipped ?? 0}</p><p class="m-0" style="min-height: 15px;">${data.quantityInStock ?? 0}</p>`;
                }
            },
            {
                data: (row) => (
                    {
                        companyName: row.companyName,
                        contactName: row.contactName,
                    }
                ),
                name: 'companyName_contactName',
                title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Company</div>Contact`,
                className: 'text-wrap text-break',
                render: (data, type, row) => {
                    let escapedCompanyName = "";
                    let escapedContactName = "";

                    if (data.companyName) {
                        const companyNameText = GlobalTrader.StringHelper.setCleanTextValue(data.companyName);
                        escapedCompanyName = DataTable.render.text().display(companyNameText);
                    }
                    if (data.contactName) {
                        const contactNameText = GlobalTrader.StringHelper.setCleanTextValue(data.contactName);
                        escapedContactName = DataTable.render.text().display(contactNameText);
                    }

                    return `<p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" ${row.isMakeRowYellow ? 'style=\"background-color:yellow;\"' : ''} href="${GlobalTrader.PageUrlHelper.Get_URL_Company(row.companyNo)}">${escapedCompanyName}</a> </p>
                        <p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Contact(row.contactNo)}">${escapedContactName}</a> </p>`;
                }
            },
            {
                name: 'dateOrdered',
                data: 'dateOrdered',
                title: 'Ordered',
                className: 'text-wrap',
                render: (data, type, row) => {
                    return `
                                <div class="m-0" style="min-height: 15px;">
                                   ${row.dateOrdered} 
                                </div>
                            `
                }
            },
            {
                name: 'datePromised',
                data: 'datePromised',
                title: 'Promised',
                width: '62px',
                className: 'text-wrap',
                render: (data, type, row) => {
                    let chkdatestatus = '';
                    let visibility = 'visible';
                    if (row.datePromisedStatus == 'Green')
                        chkdatestatus = 'green';
                    else if (row.datePromisedStatus == 'Amber')
                        chkdatestatus = '#FFBF00';
                    else if (row.datePromisedStatus == 'Red')
                        chkdatestatus = 'Red';
                    else {
                        chkdatestatus = 'White';
                        visibility = 'hidden';
                    }

                    return `
                                <div class="m-0" style="min-height: 15px; display: flex; flex-direction: column; gap: 4px;">
                                    <span>${row.datePromised ?? ""}</span>
                                    <span style="background-color: ${chkdatestatus} !important; height: 20px; width: 20px; visibility: ${visibility}; margin-left: auto;"></span>
                                </div>
                            `
                }
            },
            {
                name: 'status',
                data: 'status',
                title: 'Status',
                width: '10%',
                className: 'text-wrap text-break',
                render: (data, type, row) => {
                    return `
                                <div class="m-0" style="min-height: 15px;">${row.statusDesc}</div>
                            `
                }
            },
            {
                name: 'contractNo',
                data: 'contractNo',
                title: 'Contract No',
                className: 'text-wrap text-break',
                render: (data, type, row) => {
                    return `
                                <div class="m-0" style="min-height: 15px;">
                                   ${row.contractNo ?? ""} 
                                </div>
                            `
                }
            },
            {
                data: (row) => (
                    {
                        
                    }
                ),
                name: 'todoList',
                title: `${salesOrdersLocalizer.ToDoList}`,
                className: 'text-wrap text-break',
                width: '80px',
                render: (data, type, row) => {
                    let taskCountText = row.taskCount ?? 0;
                    let encodedCompanyName = GlobalTrader.StringHelper.htmlAttributeEncode(row.companyName);
                    const viewTaskTitle = row.hasUnFinishedTask > 0 ? salesOrdersLocalizer.needToCompleteOpenTasks : salesOrdersLocalizer.ViewTask;
                    const viewTaskStyle = row.hasUnFinishedTask > 0 ? 'style="color: red !important;"' : "";
                    const viewTaskButtonHtml = `<a title="${viewTaskTitle}" ${viewTaskStyle} class="dt-hyper-link" href="/Profile/ToDo?so=${row.salesOrderNumber}&Category=4">
                            ${taskCountText} ${salesOrdersLocalizer.Task}
                        </a>
                        `;
                    return `<div class="mb-1 text-wrap text-break" style="min-height: 15px;">
                                <div class="d-flex flex-wrap" style="column-gap: 0.5rem; row-gap: 0.25rem;">
                                    <a onClick="openSpecificCategoryToDoDialog(event)"
                                        data-sales-order-no="${row.salesOrderNo}" 
                                        data-sales-order-number="${row.salesOrderNumber}" 
                                        data-company-name="${encodedCompanyName}" 
                                        data-category-type="${SALES_ORDER_TASK_CATEGORY}"
                                        class="dt-hyper-link add-task" href="#">${salesOrdersLocalizer.AddTask}</a>
                                    ${viewTaskButtonHtml}                                
                                </div>
                            </div>
                            `
                }
            },
        ]
        this.showFilterButtons(false);  //hide all button filters at init
        this.$salesOrdersTable = $('#salesOrdersTbl')
            .DataTable({
                serverSide: true,
                ajax: {
                    url: '/api/orders/sales-orders/search-sales-orders',
                    type: 'POST',
                    contentType: 'application/json',
                    beforeSend: (xhr) => {
                        this.currentXhr = xhr;
                        this.tableFilter.toggleApplyCancelButtons(false);
                        this.$warningSection.hide();
                    },
                    error: function (xhr, status, error) {
                        console.log("AJAX Error: ", status, error);
                    },
                    data: (data) => {
                        let sortDir = data.order.length !== 0 ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : this.salesOrdersConfig.defaultSortDirection;
                        const filtersData = this.tableFilter.getDisplayedFilterValues();

                        if (this.tableFilter.state !== this.tableFilter.ViewState.INVISIBLE) {
                            return JSON.stringify({
                                draw: data.draw,
                                start: data.start,
                                length: data.length,
                                sortDir: sortDir,
                                viewLevel: this.currentTab,
                                orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex,
                                filters: filtersData,
                                saveStates: this.isLockFilter
                            });
                        } else {
                            return JSON.stringify({
                                draw: data.draw,
                                start: data.start,
                                length: data.length,
                                sortDir: sortDir,
                                viewLevel: this.currentTab,
                                orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex
                            });
                        }
                    },
                },
                info: true,
                scrollCollapse: true,
                responsive: true,
                select: false,
                displayStart: this.defaultPageIndex * this.pageSize,
                paging: true,
                ordering: true,
                order: [[this.sortIndex, GlobalTrader.SortHelper.getSortDirNameById(this.sortDirection)]],
                columnDefs: [
                    {
                        "orderable": false,
                        "targets": [0,7,9]
                    },
                    { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
                ],
                searching: false,
                pageLength: this.pageSize,
                lengthMenu: [5, 10, 25, 50],
                dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                    '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                    '<"dt-layout-cell dt-layout-end" p >><"clear">',
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                    infoFiltered: "",
                    lengthMenu: "_MENU_ per page",
                },
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                columns: tableColumnsDefine,
                rowId: 'salesOrderLineId',
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                },
            })
            
            .on('draw.dt', () => {
                this.$salesOrdersSectionBox.section_box("option", "loading", false);
                $('#sales-orders-box-content').show();
                this.$salesOrdersTable.columns.adjust();
                // Remove neutral sorting icon
                const tableId = this.$salesOrdersTable.table().node().id;
                $(`#${tableId} thead th`)
                    .removeClass('dt-orderable-asc dt-orderable-desc')
                    .addClass('position-relative');

                $(`#${tableId} thead th:not(.dt-orderable-none)`)
                    .attr('role', 'button');

                $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');

                $(`#${tableId} .badge-hover-text`).each((index, element) => {
                    const handleOnStopMouseEnterDebounce = DebounceHelper.debounce(this.handleOnStopMouseEnter, 100)
                    const handleOnStopMouseLeaveDebounce = DebounceHelper.debounce(this.handleOnStopMouseLeave, 2000)

                    let mouseLeaveTimeoutTask;

                    $(element)
                        .on("mouseenter", (event) => {
                            // Show on stop label 
                            handleOnStopMouseEnterDebounce(event, element);

                            // Cancel hide on stop label
                            if (mouseLeaveTimeoutTask) clearTimeout(mouseLeaveTimeoutTask);
                        })
                        .on("mouseleave", (event) => {

                            // Hide on stop label 
                            mouseLeaveTimeoutTask = handleOnStopMouseLeaveDebounce(event, element);
                        });
                })
            })
            .on('processing.dt', (e, settings, processing) => {
                if (processing) {
                    // Table is processing (e.g., AJAX call happening)
                    this.$salesOrdersSectionBox.section_box("option", "loading", true);
                    this.showFilterButtons(false);
                    if (this.tableFilter.state !== this.tableFilter.ViewState.VISIBLE) {
                        this.tableFilter.toggleApplyCancelButtons(false);
                    }
                } else {
                    // Done processing
                    this.tableFilter.updateAppliedFilterText();
                    this.$salesOrdersSectionBox.section_box("option", "loading", false);
                    $('#sales-orders-box-content').show();
                    this.showFilterButtons(true);

                    if (this.tableFilter.state === this.tableFilter.ViewState.INVISIBLE) {
                        this.tableFilter.toggleFilterVisible(false);
                        this.tableFilter.setState(this.tableFilter.ViewState.INVISIBLE)
                    } else if (this.tableFilter.state !== this.tableFilter.ViewState.VISIBLE) {
                        this.tableFilter.toggleFilterVisible(false);
                    }
                    this.tableFilter.toggleApplyCancelButtons(true);
                }
            })


        $('button[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            this.currentTab = $(e.target).data('view-level');
            this.hideSalesmanFilter(this.currentTab);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$salesOrdersTable, true);
        });

        this.$lockUnlockButton.on("click", () => {
            this.isLockFilter = !this.isLockFilter;
            this.setLockOrUnlockFilter(this.isLockFilter);
        })

        this.$exportExcelButton.on('click', async (e) => {
            const button = e.currentTarget;
            button.disabled = true;
            try {
                await this.exportSalesOrderData();
            } finally {
                button.disabled = false;
            }
        });
    }

    setLockOrUnlockFilter(isLock) {
        const innerHtml = isLock ? '<img alt="lock-button" src="/img/icons/lock.svg">' : '<img alt="lock-button" src="/img/icons/lock-open.svg">';
        this.$lockUnlockButton.html(innerHtml);
    }

    hideSalesmanFilter(viewLevel) {
        const inputs = this.tableFilter.getInputs();
        viewLevel == 0 ? inputs["Salesman"].instance.wrapper.hide() : inputs["Salesman"].instance.wrapper.show();
    }
    hideClientName(isHide) {
        const inputs = this.tableFilter.getInputs();
        isHide ? inputs["ClientName"].instance.wrapper.hide() : inputs["ClientName"].instance.wrapper.show();
    }

    handleOnStopMouseEnter(event, element) {
        event.stopPropagation()
        event.preventDefault()

        $(element).find("span.text").addClass("d-inline")
    }

    handleOnStopMouseLeave(event, element) {
        event.stopPropagation()
        event.preventDefault()

        $(element).find("span.text").removeClass("d-inline")
    }

    addClientNameFilterEvent() {
        if (!this.salesOrdersConfig.isGlobalLogin) return;
        const clientNameInput = this.tableFilter.getInputElementByName("ClientName");
        clientNameInput.on("checkboxChanged.mfi", (e) => {
            const isChecked = e.target.checked;
            const selectedClient = clientNameInput.getValue().value || null;
            if (isChecked && selectedClient != this.clientFilterState.params.clientNo) {
                this.clientFilterState.params.clientNo = selectedClient;
                this.reloadSalesPersonDropdown();
                this.reloadCountryDropdown();
                this.UnCheckedInputFilterByName("Salesman");
                this.UnCheckedInputFilterByName("Country");
            } else if (selectedClient) {
                this.clientFilterState.params.clientNo = null;
                this.reloadSalesPersonDropdown();
                this.reloadCountryDropdown();
                this.UnCheckedInputFilterByName("Salesman");
                this.UnCheckedInputFilterByName("Country");
            }
        });
    }

    UnCheckedInputFilterByName(fieldName) {
        this.tableFilter.getInputElementByName(fieldName).requiredCheckbox
            .prop('checked', false)
            .trigger("change");
    }

    reloadSalesPersonDropdown() {
        let params = {
            clientNo: this.clientFilterState.params.clientNo
        }
        const $salesmanDropdown = $("#sales-orders-box-content #Salesman");
        $salesmanDropdown.dropdown("reset");
        $salesmanDropdown.dropdown("reload", this.clientFilterState.salespersonEndpoint, params);
    }

    reloadCountryDropdown() {
        let params = {
            clientId: this.clientFilterState.params.clientNo
        }
        const $countryDropdown = $("#sales-orders-box-content #Country");
        $countryDropdown.dropdown("reset");
        $countryDropdown.dropdown("reload", this.clientFilterState.countriesEndpoint, params);
    }

    showFilterButtons(isShow) {
        if (isShow) {
            this.$lockUnlockButton.removeClass('force-disabled-content');
            this.tableFilter.enableButtons(['apply', 'off', 'hide', 'reset', 'show']);
        } else {
            this.$lockUnlockButton.addClass('force-disabled-content');
            this.tableFilter.disableButtons(['apply', 'off', 'hide', 'reset', 'show']);
        }
    }

    _onSaveAddTaskComplete() {
        this._$addToDoDialog.on('addTodoSuccess', (e) => {
            this.refreshDatatable();              
        });
    }

    async exportSalesOrderData() {
        const totalRecordsToExport = $('#salesOrdersTbl').DataTable().page.info().recordsTotal; 
        const filterParams = $('#salesOrdersTbl').DataTable().ajax.params();

        await GlobalTrader.ApiClient.postDownloadFileAsync(
            "orders/sales-orders/download-excel",
            {
                ...JSON.parse(filterParams),
                length: totalRecordsToExport,
                start: 0,
            },
            {
                timeoutMilliseconds: 500 * 1000,
            },
        );
    }
} 