﻿@page
@using GlobalTrader2.Core.Enums
@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseOrder.Details.IndexModel
@inject SettingManager _settingManager
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@{
    ViewData["Title"] = $"Purchase Order {Model.PurchaseOrderGeneralInfo.PurchaseOrderNumber}";

}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}


<div class="page-content-container mh-1500">
    <div class="d-flex justify-content-between mt-2">
        <h4 class="page-primary-title" id="company-detail-primary-title">
            <span id="company-name-primary-title">Purchase Order @Model.PurchaseOrderGeneralInfo.PurchaseOrderNumber</span>
        </h4>
    </div>
    <div class="mb-3 border-bottom">
        <div class="row m-0 mb-1">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <span>
                    <a class="dt-hyper-link " href="/Contact/AllCompanies/Details?cm=@Model.PurchaseOrderGeneralInfo.CompanyNo">@Model.PurchaseOrderGeneralInfo.CompanyName</a>
                </span>
                <span>
                    @if (!string.IsNullOrEmpty(Model.PurchaseOrderGeneralInfo.AdvisoryNotes))
                    {
                        <img src="/img/icons/circle-exclamation-red.svg" alt="AdvisoryNotes" title="@Model.PurchaseOrderGeneralInfo.AdvisoryNotes" class="ms-1 rounded-circle bg-white" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom" />
                    }

                </span>
            </div>
        </div>
        <div class="d-flex justify-content-between m-0 mb-1">
            <div class="p-0 fs-11 text-primary-bold d-flex gap-2">
                <span class="fw-bold">@_commonLocalizer["Status"]</span>
                <span class="type-name">@Model.PurchaseOrderGeneralInfo.Status</span>
            </div>

        </div>
        @if (Model.IsDataHasOtherClient)
        {
            <div class="row m-0 mb-1">
                <div class="col-12 p-0 fs-12 text-primary-bold">
                    <span style="background-color: yellow;">
                        @Model.PurchaseOrderGeneralInfo.ClientName
                    </span>
                </div>
            </div>
        }
        <div id="risk-warning-section" class="alert alert-warning d-flex align-items-center gap-2 d-none" role="alert" style="display: none">
            <img id="risk-icon" src="/img/icons/x-octagon.svg" class="d-none" alt="risk-warning"/>
            <span id="risk-text" class="fs-12 text-primary-bold text-red d-none" ></span>
        </div>
        <div id="sanction-warning-section" class="alert alert-warning d-flex align-items-center gap-2 d-none" role="alert" style="display: none">
            <img id="sanction-icon" src="/img/icons/x-octagon.svg" class="d-none" alt="sanction-warning"/>
            <span id="sanction-text" class="fs-12 text-primary-bold text-red d-none"></span>
        </div>
    </div>

    @await Html.PartialAsync("MainInfo/_MainInfo.cshtml", Model)
</div>
<script>
    const localizedStrings = {
        sanctionMessage : "@_messageLocalizer["Please note this PO is being shipped from a Sanctioned company. Please check the ship from address and ensure this is correct before aproval."]",
    }
</script>


@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")

    <script src="@_settingManager.GetCdnUrl("/js/widgets/loading-spinner.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/dialog-custom-events.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datatables-detail-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/api-client.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/custom-input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/sort-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/search-select.js")" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/orders/purchase-orders/details/purchase-order-details.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/orders-purchase-orders-details.bundle.js" asp-append-version="true"></script>
    </environment>
}