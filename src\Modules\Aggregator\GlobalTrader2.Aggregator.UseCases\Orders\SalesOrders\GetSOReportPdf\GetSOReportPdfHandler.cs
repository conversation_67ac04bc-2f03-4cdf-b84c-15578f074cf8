﻿using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.Address;
using GlobalTrader2.Dto.File;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetCurrencyRateCurrentAtDate;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetAllSoLinesBySoId;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportManualStock;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPO;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPOStock;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportShipped;

namespace GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetSOReportPdf
{
    public class GetSOReportPdfHandler : IRequestHandler<GetSOReportPdfQuery, BaseResponse<FileUploadItem>>
    {
        private readonly IRazorViewToStringService _razorViewToStringService;
        private readonly IBaseRepository<SalesOrderForPrintReadModel> _salesOrderForPrintRepository;
        private readonly IMapper _mapper;
        private readonly ISender _sender;
        private readonly IPdfService _pdfService;
        private readonly IBaseRepository<AddressSelect> _addressRepository;
        private const string allocatedImagePath = "allocated.gif";
        private const string shippedImagePath = "shipped.gif";
        private const string ihsPartstatusPath = "ihspartstatuspng.png";
        private const string hazardousPath = "Hazardous.png";
        public GetSOReportPdfHandler(IBaseRepository<SalesOrderForPrintReadModel> salesOrderForPrintRepository
            , IRazorViewToStringService razorViewToStringService
            , IMapper mapper
            , ISender sender
            , IPdfService pdfService
            , IBaseRepository<AddressSelect> addressRepository)
        {
            _salesOrderForPrintRepository = salesOrderForPrintRepository;
            _razorViewToStringService = razorViewToStringService;
            _mapper = mapper;
            _sender = sender;
            _pdfService = pdfService;
            _addressRepository = addressRepository;
        }
        public async Task<BaseResponse<FileUploadItem>> Handle(GetSOReportPdfQuery request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>() { new SqlParameter("SalesOrderId", SqlDbType.Int) { Value = request.SalesOrderId } };
            var salesOrderForPrintQuery = await _salesOrderForPrintRepository
                                    .SqlQueryRawAsync($"{StoredProcedures.Get_SalesOrder_for_Print} @SalesOrderId", parameters.ToArray());
            var salesOrderForPrint = salesOrderForPrintQuery.Count > 0 ? salesOrderForPrintQuery[0] : null;
            if (salesOrderForPrint != null)
            {
                var salesOrderForPrintData = _mapper.Map<SalesOrderForPrintDto>(salesOrderForPrint);
                salesOrderForPrintData.LoginFullName = request.LoginFullName;
                salesOrderForPrintData.AllocatedPath = allocatedImagePath;
                salesOrderForPrintData.ShippedPath = shippedImagePath;
                salesOrderForPrintData.IhsPartstatusPath = ihsPartstatusPath;
                salesOrderForPrintData.HazardousPath = hazardousPath;
                salesOrderForPrintData.BaseDirectoryPath = request.BaseDirectoryPath;
                var addressParams = new[]
                {
                        new SqlParameter("@AddressId", SqlDbType.Int) { Value = salesOrderForPrintData.ShipToAddressNo }
                 };
                var adddressQuery = await _addressRepository.SqlQueryRawAsync(
                    $@"{StoredProcedures.Select_Address} @AddressId", addressParams);
                if (adddressQuery != null)
                {
                    var address = _mapper.Map<AddressDto>(adddressQuery.FirstOrDefault());
                    salesOrderForPrintData.ShipToAdressName = address.ToLongString();
                }
                DateTime currencyDate = salesOrderForPrintData.CurrencyDate == null ? salesOrderForPrintData.DateOrdered : (DateTime)salesOrderForPrintData.CurrencyDate;
                var currencyRateRes = await _sender.Send(new GetCurrencyRateCurrentAtDateQuery(salesOrderForPrintData.CurrencyNo, currencyDate), cancellationToken);
                if (currencyRateRes.Success)
                {
                    salesOrderForPrintData.CurrencyRate = Convert.ToDecimal(currencyRateRes.Data ?? 0);
                }
                var soLine = await _sender.Send(new GetAllSoLinesBySoIdQuery(salesOrderForPrintData.SalesOrderId, false)
                                                         , cancellationToken);
                if (soLine != null && soLine.Success && soLine.Data != null && soLine.Data.Any())
                {
                    var salesOrderLineList = _mapper.Map<List<SalesOrderLineForSOReportDto>>(soLine.Data ?? []);
                    foreach (var salesOrderLine in salesOrderLineList)
                    {
                        decimal soPriceInBase = Functions.ConvertValueToBaseCurrencyDecimal(Convert.ToDecimal(salesOrderLine.Price), salesOrderForPrintData.CurrencyRate);
                        decimal lineTotalSell = Convert.ToDecimal(salesOrderLine.Quantity) * soPriceInBase;
                        salesOrderLine.SoPriceInBase = soPriceInBase;
                        salesOrderLine.LineTotalSell = lineTotalSell;
                        var salesOrderLineReportPoStockQuery = await _sender.Send(new GetSalesOrderLineReportPoStockQuery(salesOrderLine.SalesOrderLineId)
                                                                                        , cancellationToken);
                        if (salesOrderLineReportPoStockQuery != null && salesOrderLineReportPoStockQuery.Success && salesOrderLineReportPoStockQuery.Data != null)
                        {

                            salesOrderLine.SalesOrderLineReportPoStockList = salesOrderLineReportPoStockQuery.Data.ToList();
                        }
                        var salesOrderLineReportPoQuery = await _sender.Send(new GetSalesOrderLineReportPoQuery(salesOrderLine.SalesOrderLineId)
                                                                                        , cancellationToken);
                        if (salesOrderLineReportPoQuery != null && salesOrderLineReportPoQuery.Success && salesOrderLineReportPoQuery.Data != null)
                        {

                            salesOrderLine.SalesOrderLineReportPoList = salesOrderLineReportPoQuery.Data.ToList();
                        }
                        var salesOrderLineReportManualStockQuery = await _sender.Send(new GetSalesOrderLineReportManualStockQuery(salesOrderLine.SalesOrderLineId)
                                                                                        , cancellationToken);
                        if (salesOrderLineReportManualStockQuery != null && salesOrderLineReportManualStockQuery.Success && salesOrderLineReportManualStockQuery.Data != null)
                        {
                            salesOrderLine.SalesOrderLineReportManualStockList = salesOrderLineReportManualStockQuery.Data.ToList();
                        }
                        var salesOrderLineReportShippedQuery = await _sender.Send(new GetSalesOrderLineReportShippedQuery(salesOrderLine.SalesOrderLineId)
                                                                                        , cancellationToken);
                        if (salesOrderLineReportShippedQuery != null && salesOrderLineReportShippedQuery.Success && salesOrderLineReportShippedQuery.Data != null)
                        {
                            salesOrderLine.SalesOrderLineReportShippedList = salesOrderLineReportShippedQuery.Data.ToList();
                        }
                    }
                    salesOrderForPrintData.SalesOrderLineList = salesOrderLineList;
                }
                var pdfName = Convert.ToString(request.SalesOrderId) + "_" + string.Format("{0}_{1}_{2}_{3}.pdf", request.ClientId, "SOREPORT", salesOrderForPrint.SalesOrderNumber.ToString(), DateTime.Now.ToString("yyyyMMddHHmmssfff"));
                salesOrderForPrintData.FileName = pdfName;
                var html = await _razorViewToStringService.RenderViewToStringAsync("Templates/Pdf/_SOReportPDF", salesOrderForPrintData);

                var pdf = await _pdfService.CreateSOReportFromPdfAsync(html);
                return new BaseResponse<FileUploadItem>()
                {
                    Success = true,
                    Data = new FileUploadItem()
                    {
                        FileName = pdfName,
                        File = new MemoryStream(pdf),
                    },
                };
            }
            return new BaseResponse<FileUploadItem>()
            {
                Success = false,
                Data = null
            };
        }

    }
}
