using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Core.Interfaces;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine
{
    public class EditSalesOrderLineHandler(IBaseRepository<AffectedRows> _repository,
    IEmailService _emailService, IEmailTemplate _emailTemplateService) : IRequestHandler<EditSalesOrderLineCommand, BaseResponse<bool>>
    {

        public async Task<BaseResponse<bool>> Handle(EditSalesOrderLineCommand request, CancellationToken cancellationToken)
        {
            await UpdateSalesOrderLine(request);
            _ = SendEmailNotification(request, cancellationToken);
            return new BaseResponse<bool> { Data = true, Success = true };
        }

        private async Task UpdateSalesOrderLine(EditSalesOrderLineCommand request)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@SalesOrderLineId", SqlDbType.Int) { Value = request.SalesOrderLineId },
                new SqlParameter("@Part", SqlDbType.NVarChar) { Value = request.Part ?? (object)DBNull.Value },
                new SqlParameter("@ManufacturerNo", SqlDbType.Int) { Value = request.ManufacturerNo ?? (object)DBNull.Value },
                new SqlParameter("@DateCode", SqlDbType.NVarChar) { Value = request.DateCode ?? (object)DBNull.Value },
                new SqlParameter("@PackageNo", SqlDbType.Int) { Value = request.PackageNo ?? (object)DBNull.Value },
                new SqlParameter("@Quantity", SqlDbType.Int) { Value = request.Quantity },
                new SqlParameter("@Price", SqlDbType.Float) { Value = request.Price },
                new SqlParameter("@DatePromised", SqlDbType.DateTime) { Value = request.DatePromised },
                new SqlParameter("@RequiredDate", SqlDbType.DateTime) { Value = request.RequiredDate },
                new SqlParameter("@Instructions", SqlDbType.NVarChar) { Value = request.Instructions ?? (object)DBNull.Value },
                new SqlParameter("@ProductNo", SqlDbType.Int) { Value = request.ProductNo ?? (object)DBNull.Value },
                new SqlParameter("@Taxable", SqlDbType.NVarChar) { Value = request.Taxable ?? (object)DBNull.Value },
                new SqlParameter("@CustomerPart", SqlDbType.NVarChar) { Value = request.CustomerPart ?? (object)DBNull.Value },
                new SqlParameter("@ShipASAP", SqlDbType.Bit) { Value = request.ShipASAP ?? false },
                new SqlParameter("@Inactive", SqlDbType.Bit) { Value = request.Inactive },
                new SqlParameter("@ROHS", SqlDbType.TinyInt) { Value = request.ROHS ?? (object)DBNull.Value },
                new SqlParameter("@Notes", SqlDbType.NVarChar) { Value = request.Notes ?? (object)DBNull.Value },
                new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = request.UpdatedBy },
                new SqlParameter("@ProductSource", SqlDbType.TinyInt) { Value = request.ProductSource ?? (object)DBNull.Value },
                new SqlParameter("@PODelDate", SqlDbType.DateTime) { Value = request.PODelDate ?? (object)DBNull.Value },
                new SqlParameter("@SOSerialNo", SqlDbType.Int) { Value = request.SOSerialNo ?? 0 },
                new SqlParameter("@PrintHazardous", SqlDbType.Bit) { Value = request.PrintHazardous ?? false },
                new SqlParameter("@MSLLevel", SqlDbType.NVarChar) { Value = request.MSLLevel ?? (object)DBNull.Value },
                new SqlParameter("@ContractNo", SqlDbType.NVarChar) { Value = request.ContractNo ?? (object)DBNull.Value },
                new SqlParameter("@IsFormChanged", SqlDbType.Bit) { Value = request.IsFormChanged ?? false },
                new SqlParameter("@IsReasonChanged", SqlDbType.Bit) { Value = request.IsReasonChanged ?? false },
                new SqlParameter("@PromiseReasonNo", SqlDbType.Int) { Value = request.PromiseReasonNo ?? (object)DBNull.Value },
                new SqlParameter("@ECCNCode", SqlDbType.NVarChar) { Value = request.ECCNCode ?? (object)DBNull.Value },
                new SqlParameter("@ECCNNo", SqlDbType.Int) { Value = request.ECCNNo ?? (object)DBNull.Value },
                new SqlParameter("@ServiceCostRef", SqlDbType.Float) { Value = request.ServiceCostRef ?? (object)DBNull.Value },
                new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output }
            };

            await _repository.SqlQueryRawReturnValueAsync(
                sql: $"{StoredProcedures.Update_SalesOrderLine} " +
                    "@SalesOrderLineId, @Part, @ManufacturerNo, @DateCode, @PackageNo, " +
                    "@Quantity, @Price, @DatePromised, @RequiredDate, @Instructions, " +
                    "@ProductNo, @Taxable, @CustomerPart, @ShipASAP, @Inactive, " +
                    "@ROHS, @Notes, @UpdatedBy, @ProductSource, @PODelDate, " +
                    "@SOSerialNo, @PrintHazardous, @MSLLevel, @ContractNo, @IsFormChanged, " +
                    "@IsReasonChanged, @PromiseReasonNo, @ECCNCode, @ECCNNo, @ServiceCostRef, " +
                    "@RowsAffected output",
                parameters: parameters.ToArray());
        }

        private async Task SendEmailNotification(EditSalesOrderLineCommand request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.LoginEmail) || request.PreviousPromiseDate == request.DatePromised) return;

            var subject = $"SO {request.SalesOrderNumber} Promised Date Changed";
            var body = _emailTemplateService.PopulateTemplate("PromiseReasonEmail",
                new Dictionary<string, string>
                {
                    { "SO_NUMBER", request.SalesOrderNumber.ToString() },
                    { "PROMISE_DATE", Functions.FormatDate(request.DatePromised) },
                    { "REASON", request.PromiseReasonString ?? string.Empty },
                    { "SENDER_NAME", request.LoginFullName }
                });

            await _emailService.TrySendEmailAsync(
                fromEmail: request.LoginEmail,
                toEmail: [request.Email],
                cc: [],
                bcc: [],
                subject: subject,
                body: body,
                replyTos: [request.LoginEmail],
                attachments: [],
                cancellationToken: cancellationToken);
        }
    }
}