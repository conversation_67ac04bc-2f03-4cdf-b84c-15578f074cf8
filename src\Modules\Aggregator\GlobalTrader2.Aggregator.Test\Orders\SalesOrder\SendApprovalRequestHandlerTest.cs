﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core;
using MediatR;
using Moq;
using AutoFixture;
using AutoFixture.AutoMoq;
using System.Linq.Expressions;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.SendApprovalRequest;
using Microsoft.Data.SqlClient;
using GlobalTrader2.Aggregator.UseCases.Account.LoginForSendEmai.LoginForSendEmai.Queries;
using GlobalTrader2.Aggregator.UseCases.Account.LoginForSendEmai.Queries.Dtos;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.UploadSalesOrderReportPdf;

namespace GlobalTrader2.Aggregator.Test.Orders.SalesOrder
{
    public class SendApprovalRequestHandlerTest
    {
        private readonly Mock<IBaseRepository<SorDetailForPowerAppReadModel>> _sorDetailForPowerAppRepository;
        private readonly Mock<ISender> _sender;
        private readonly Mock<IBaseRepository<SalesOrderOpenLineSummaryValuesModel>> _saleOrderTotalRepository;
        private readonly Mock<IBaseRepository<SalesOrderForCompanyReadModel>> _salesOrderOpenRepository;
        private readonly Mock<IPowerAutomateApiClient> _powerAutomateApiClient;
        private readonly Mock<IBaseRepository<PowerAppUrl>> _powerappUrlRepository;
        private readonly SendApprovalRequestHandler _handler;
        private readonly IFixture _fixture;
        public SendApprovalRequestHandlerTest()
        {
            _sorDetailForPowerAppRepository = new Mock<IBaseRepository<SorDetailForPowerAppReadModel>>();
            _sender = new Mock<ISender>();
            _saleOrderTotalRepository = new Mock<IBaseRepository<SalesOrderOpenLineSummaryValuesModel>>();
            _salesOrderOpenRepository = new Mock<IBaseRepository<SalesOrderForCompanyReadModel>>();
            _powerAutomateApiClient = new Mock<IPowerAutomateApiClient>();
            _powerappUrlRepository = new Mock<IBaseRepository<PowerAppUrl>>();
            _fixture = new Fixture();
            SetupMocks();
            _handler = new SendApprovalRequestHandler(
                _sorDetailForPowerAppRepository.Object,
                _sender.Object,
                _saleOrderTotalRepository.Object,
                _salesOrderOpenRepository.Object,
                _powerAutomateApiClient.Object,
                _powerappUrlRepository.Object);

        }
        private void SetupMocks()
        {
            _sender.Setup(m => m.Send(It.IsAny<UploadSalesOrderReportPdfCommand>(), It.IsAny<CancellationToken>()))
             .ReturnsAsync(new BaseResponse<string> { Data = "filePath", Success = true }).Verifiable();
            _sorDetailForPowerAppRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(new List<SorDetailForPowerAppReadModel>
                {
                    new SorDetailForPowerAppReadModel
                    {
                        CreditLimit = 10000,
                        Balance = 2000,
                        CompanyNo = 1,
                        IsAuthorised = 0,
                        IsSoCompleted = 0,
                        IsPODocExists = 1
                    }
                });

            _saleOrderTotalRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsUsingFixture(_fixture);

            _salesOrderOpenRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsUsingFixture(_fixture);

            _powerappUrlRepository.Setup(r => r.GetAsync(It.IsAny<Expression<Func<PowerAppUrl,bool>>>()))
                .ReturnsUsingFixture(_fixture);

            _powerAutomateApiClient.Setup(c => c.SendSOApprovalToTeam(It.IsAny<string>(), It.IsAny<Dictionary<string,string>>()))
                .Returns(Task.CompletedTask).Verifiable();
            _sender.Setup(z=>z.Send(It.IsAny<GetLoginForSendEmailQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<LoginForSendEmailDto>()
                {
                    Success = true
                ,
                    Data = new LoginForSendEmailDto
                    {
                        Email = "<EMAIL>"
                    }
                }
                );
        }

        [Fact]
        public async Task SendApprovalRequestHandler_SuccessTest()
        {
            var request = new SendApprovalRequestCommand()
            {
                SalesOrderId = 1,
                LoginId = 1,
                Subject = "Test Approval Request",
                Message = "Please approve this sales order.",
                ApproverIds = new List<int> { 1, 2, 3 },
                IsNotifySO = true,
                Uri = "https://example.com",
                V1Uri= "https://example.com",
                ClientId = 101,
                BaseDirectory = "path/to/image",
                LoginFullName = "Test User"
            };
            var result = await _handler.Handle(request, CancellationToken.None);
            Assert.True(result.Success);
            Assert.True(result.Data);


            _powerAutomateApiClient.Verify(x => x.SendSOApprovalToTeam(
                It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()
            ), Times.Once);

        }
    }
}
