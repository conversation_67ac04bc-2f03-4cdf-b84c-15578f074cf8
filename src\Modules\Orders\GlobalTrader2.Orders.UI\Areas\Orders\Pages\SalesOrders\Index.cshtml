﻿@page
@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.IndexModel
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders
@using GlobalTrader2.Core.Enums
@using GlobalTrader2.Dto.DataListNugget
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LeftNugget
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderSelectionMenu
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.QuickJump
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SelectionMenu
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SourcingLinks
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Services
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using System.Text.Json
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject SessionManager _sessionManager
@inject SecurityManager _securityManager
@inject SettingManager _settingManager

@{
    ViewData["TodoDialogTittle"] = @_localizer[Model.PageTitle];
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

@section LeftSidebar {
    @await Component.InvokeAsync(nameof(LeftNugget), new LeftNuggetProps()
    {
        IsInitiallyExpanded = true,
        Item = SideBar.QuickJump,
        ChildComponent = nameof(QuickJump),
        ChildComponentParameters = new { options = QuickJumpOptions.OrderQuickJumpOptions }
    })
    @await Component.InvokeAsync(nameof(OrderSelectionMenu))
    @await Component.InvokeAsync(nameof(SourcingLinksMenu))
}

@{
    ViewData["Title"] = _localizer[Model.PageTitle];
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();

    var defaultPageSize = Model.DataListNuggetState.PageSize == 0 ? _sessionManager.GetInt32(SessionKey.DefaultListPageSize) : Model.DataListNuggetState.PageSize;
    var defaultPageIndex = Model.DataListNuggetState.Page > 0 ? Model.DataListNuggetState.Page - 1 : 0;
    var filterStatesString = string.Empty;
    dynamic filterStates = new
    {
        Part = Model.DataListNuggetState.GetTextBoxStateValue("Part"),
        Contact = Model.DataListNuggetState.GetTextBoxStateValue("Contact"),
        CMName = Model.DataListNuggetState.GetTextBoxStateValue("CMName"),
        Salesman = Model.DataListNuggetState.GetStateValue("Salesman"),
        CustPO = Model.DataListNuggetState.GetTextBoxStateValue("CustPO"),
        RecentOnly = Model.DataListNuggetState.GetStateValue("RecentOnly"),
        IncludeClosed = Model.DataListNuggetState.GetStateValue("IncludeClosed"),
        SONo = Model.DataListNuggetState.GetStateValue("SONo"),
        DateOrderedFrom = Model.DataListNuggetState.GetStateValue("DateOrderedFrom"),
        DateOrderedTo = Model.DataListNuggetState.GetStateValue("DateOrderedTo"),
        DatePromisedFrom = Model.DataListNuggetState.GetStateValue("DatePromisedFrom"),
        DatePromisedTo = Model.DataListNuggetState.GetStateValue("DatePromisedTo"),
        UnauthorisedOnly = Model.DataListNuggetState.GetStateValue("UnauthorisedOnly"),
        IncludeOrderSent = Model.DataListNuggetState.GetStateValue("IncludeOrderSent"),
        SOCheckedStatus = Model.DataListNuggetState.GetStateValue("SOCheckedStatus"),
        SalesOrderStatus = Model.DataListNuggetState.GetStateValue("SalesOrderStatus"),
        AS6081 = Model.DataListNuggetState.GetStateValue("AS6081")
    };

    filterStates.RecentOnly.FieldType = FieldType.CheckBox;
    filterStates.RecentOnly.Name = "RecentOnly";
    filterStates.RecentOnly.IsOn = true;
    filterStates.RecentOnly.IsShown = true;
    filterStates.RecentOnly.Value = "true";

    filterStatesString = JsonSerializer.Serialize(filterStates, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
    var filterConfig = JsonSerializer.Serialize(Model.SalesOrdersFilterConfig, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
}
<div id="sales-orders-container" class="page-content-container">
    <div class="d-flex justify-content-end mt-1 gap-2">
        @if (@Model.CanAddSalesOrders)
        {
            <a href="@Navigations.SalesOrderAdd.CtaUri" class="btn btn-primary text-end w-fit-content">
                <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                <span class="lh-base">@_localizer["Add New Sales Order"]</span>
            </a>
        }
    </div>
    <div class="d-flex justify-content-between align-items-end border-bottom">
        <div class="d-flex justify-content-center align-items-center" style="height:50px;">
            <h2 class="page-primary-title align-middle">
                @_localizer[Model.PageTitle]
            </h2>
        </div>
        <div id="nav-tabs-wrapper" role="tablist">
            <div class="nav nav-tabs border-0 justify-content-end" id="myTab">
                @{
                    void RenderTab(ViewLevelList tab, ViewLevelList currentTab)
                    {
                        var tabName = tab.ToString().ToLower();
                        bool ariaSelectedValue = currentTab == tab;
                        @if (ariaSelectedValue)
                        {
                            <button class="nav-item nav-link @(currentTab == tab ? "active" : "")" id="@($"{tabName}-tab")" data-bs-toggle="tab" data-bs-target="@($"#{tabName}")" type="button" role="tab" aria-controls="@($"{tabName}")" aria-selected="true" data-view-level="@((int)tab)">@_commonLocalizer[$"{tabName}-tab"]</button>
                        }
                        else
                        {
                            <button class="nav-item nav-link @(currentTab == tab ? "active" : "")" id="@($"{tabName}-tab")" data-bs-toggle="tab" data-bs-target="@($"#{tabName}")" type="button" role="tab" aria-controls="@($"{tabName}")" aria-selected="false" data-view-level="@((int)tab)">@_commonLocalizer[$"{tabName}-tab"]</button>
                        }
                    }
                    if (Model.VisibleTab != null)
                    {
                        foreach (var tab in Model.VisibleTab)
                        {
                            RenderTab(tab, Model.CurrentTab);
                        }
                    }
                }
            </div>
        </div>
    </div>
    <div class="tab-content mt-3" id="myTabContent">
        <div class="tab-pane fade show active" id="my" role="tabpanel" aria-labelledby="my-tab">
            <div id="sales-orders-container" class="page-content-container">
                <div data-loading="false">
                    <div id="sales-orders-box" class="@sectionBoxClasses mb-3">
                        <h3 class="@headerClasses">
                            <span class="section-box-title">
                                @_localizer[Model.PageTitle]
                            </span>
                            <span class="section-box-button-group" style="display: none">
                                <span class="d-flex gap-2">
                                    <button class="btn btn-primary" id="export-sale-orders">
                                        <img src="~/img/icons/excel.svg" alt="export icon" width="18" height="18" />
                                        <span class="lh-base">@_commonLocalizer["Export To Excel"]</span>
                                    </button>
                                </span>
                            </span>
                        </h3>
                        <div id="sales-orders-box-content" class="@contentClasses" style="display: none">
                             <div id="filter-section-wrapper"
                                 data-states='@Html.Raw(filterStatesString)' data-config="@filterConfig"></div>
                            <div id="warning-section" class="alert alert-warning" role="alert" style="display: none">
                                @_commonLocalizer["Your search was cancelled"]
                            </div>
                            <div id="salesOrders-wrapper">
                                <table id="salesOrdersTbl" class="table simple-table display responsive nowrap" data-default-page-size="@defaultPageSize" data-default-page-index="@defaultPageIndex">
                                    <thead>
                                        <tr>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <div id="sales-orders-box-footer" class="ui-accordion-header accordion-footer mt-10px">
                                <span class="gap-2 section-box-button-group">
                                    <span id="lock-unlock-icon-wrapper" data-bs-toggle="tooltip" data-bs-placement="top" title="@_commonLocalizer["Lock to save your search, unlock to clear it"]">
                                        <a href="#" class="select-menu-gtv2-refresh-button" id="lock-unlock-button">
                                            <img alt="lock-button" src="@(Model.SalesOrdersFilterConfig.InitSaveDataListState ? "/img/icons/lock.svg" : "/img/icons/lock-open.svg")">
                                        </a>
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="d-flex gap-2 justify-content-center align-items-center mb-3">
    <canvas id="myCanvasgree" style="border: 1px solid #000000; font-weight: bold; background-color: green; width:20px; height:20px"></canvas>
    <span style="font-size:10px;">Promise date more than 1 day ahead</span>
    <canvas id="myCanvasamber" style="border: 1px solid #000000;font-weight: bold; background-color: #FFBF00; width:20px; height:20px"></canvas>
    <span style="font-size:10px;">Promise Date just 1 day ahead </span>
    <canvas id="myCanvasred" style="border: 1px solid #000000;font-weight: bold; background-color: red; width:20px; height:20px"></canvas>
    <span style="font-size:10px;">Promise date same day or passed</span>
</div>
@await Html.PartialAsync("Partials/_AddTodoItem")

<script>
    const salesOrdersLocalizer = {
        ToDoList: "@_localizer["To Do List"]",
        AddTask: "@_localizer["Add Task"]",
        ViewTask: "@_localizer["View Task"]",
        Task: "@_localizer["Task"]",
        needToCompleteOpenTasks: "@_messageLocalizer["Need to complete open Tasks"]",
    }
</script>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/loading-spinner.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/dialog-custom-events.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/dropdown-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/sort-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/custom-input-directive.js")" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/orders/sales-orders/sales-orders.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/orders-sales-orders.bundle.js" asp-append-version="true"></script>
    </environment>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/search-select.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
}