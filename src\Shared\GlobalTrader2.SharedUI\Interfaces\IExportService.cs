﻿using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.SalesOrder;

namespace GlobalTrader2.SharedUI.Interfaces
{
    public interface IExportService
    {
        byte[] ExportTodoItems(IEnumerable<ToDoListTaskReadModel> todoItems);
        byte[] ExportToExcelHUBRFQ(IEnumerable<BomCustomerRequirementExportDto> bomItems);
        byte[] ExportToExcelSalesOrder(IEnumerable<SalesOrderLineExportDto> items);
    }
}
