using System.ComponentModel.DataAnnotations.Schema;

namespace GlobalTrader2.Core.Domain.Entities;
public class SalesOrderLineExportReadModel
{
    [Column("Sales Order No")]
    public int SalesOrderNo { get; set; }
    
    [Column("Part No")]
    public string? PartNo { get; set; }
    
    [Column("Customer Po No")] 
    public string CustomerPoNo { get; set; } = String.Empty;

    [Column("Qty Ordered")]
    public int QtyOrdered { get; set; }
   
    [Column("Qty Shipped")]
    public int QtyShipped { get; set; }
    
    [Column("Qty In Stock")]
    public int QtyInStock { get; set; }

    public string Company { get; set; } = String.Empty;

    public string Contact { get; set; } = String.Empty;

    public string Ordered { get; set; } = String.Empty;

    public string? Promised { get; set; } 
    
    public string Status { get; set; } = String.Empty;

    [Column("Contract No")]
    public int? ContractNo { get; set; }
}
