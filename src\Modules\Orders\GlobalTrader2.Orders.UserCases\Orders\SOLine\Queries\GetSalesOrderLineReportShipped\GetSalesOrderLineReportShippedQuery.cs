﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportShipped
{
    public class GetSalesOrderLineReportShippedQuery : IRequest<BaseResponse<IReadOnlyList<SalesOrderLineReportShippedDto>>>
    {
        public int SalesOrderLineId { get; set; }
        public GetSalesOrderLineReportShippedQuery(int SalesOrderLineId)
        {
            this.SalesOrderLineId = SalesOrderLineId;
        }
    }
}
