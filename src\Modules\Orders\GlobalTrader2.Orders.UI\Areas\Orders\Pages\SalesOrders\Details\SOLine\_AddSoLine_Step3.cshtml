﻿@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer

<span class="mb-1">
    @_localizer["Enter the details of the new Line and press Save"]
</span>
<div class="form-error-summary" style="display: none;">
    <img src="~/img/icons/x-octagon.svg" alt="X icon" />
    <div>
        <p>@_messageLocalizer["There were some problems with your form."]</p>
        <p>@_messageLocalizer["Please check below and try again."]</p>
    </div>
</div>
<form method="post" id="add-so-line-details-form" class="row common-form">
    @Html.AntiForgeryToken()
    <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Sales Order"]</p>
            <span id="add-so-line-details-sales-order-number"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Customer"]</p>
            <span id="add-so-line-details-customer-name"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-part-no-search-select" class="form-label">@_localizer["Part No"]<span class="required"> *</span></label>
            <input type="text" class="form-control form-input" id="add-so-line-details-part-no-search-select" />
            <input type="hidden" id="add-so-line-details-part-no-search-select-value" name="Part"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Inhouse AS6081 testing required?*"]</p>
            <span id="add-so-line-as6081-span"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-as6081-select" class="form-label">@_localizer["Inhouse AS6081 testing required?*"]</label>
            <select id="add-so-line-as6081-select" class="dropdown" name="AS6081">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-eccn-code" class="form-label">@_localizer["ECCN Code"]</label>
            <input type="text" class="form-control form-input" id="add-so-line-details-eccn-code-search-select" />
            <input type="hidden" id="add-so-line-details-eccn-code-search-select-value" name="ECCNNo"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Service"]</p>
            <span id="add-so-line-details-service" name="ServiceName"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-description" class="form-label">@_localizer["Description"]</label>
            <input type="text" class="form-control form-input" id="add-so-line-details-service-description" name="ServiceDescription" maxlength="30">
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-rohs" class="form-label">@_localizer["RoHS"]</label>
            <select id="add-so-line-details-rohs" class="dropdown" name="ROHS">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-date-code" class="form-label">@_localizer["Date Code"]</label>
            <input type="text" class="form-control form-input" id="add-so-line-details-date-code" maxlength="5" name="DateCode">
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-quantity" class="form-label">@_localizer["Quantity"]<span class="required"> *</span></label>
            <input type="text" class="form-control form-input" id="add-so-line-details-quantity-input" name="Quantity"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-cost" class="form-label">@_localizer["Cost"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center gap-1">
                <input type="text" class="form-control form-input special-input" id="add-so-line-details-cost-input" name="Cost"/>
                <span id="add-so-line-details-cost-currency"></span>
            </div>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Landed Cost"]</p>
            <span id="add-so-line-details-landed-cost"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-price" class="form-label">@_localizer["Price"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center gap-1">
                <input type="text" class="form-control form-input special-input" id="add-so-line-details-price-input" name="Price"/>
                <span id="add-so-line-details-price-currency"></span>
            </div>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-cust-part-no" class="form-label">@_localizer["Cust Part No"]</label>
            <input type="text" class="form-control form-input" id="add-so-line-details-cust-part-no" name="CustomerPart"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-manufacturer" class="form-label">@_localizer["Manufacturer"]</label>
            <input type="text" class="form-control form-input" id="add-so-line-details-manufacturer-search-select"/>
            <input type="hidden" id="add-so-line-details-manufacturer-search-select-value" name="ManufacturerNo"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-product" class="form-label">@_localizer["Product"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <input type="text" class="form-control form-input" id="add-so-line-details-product-search-select"/>
            <input type="hidden" id="add-so-line-details-product-search-select-value" name="ProductNo"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-package" class="form-label">@_localizer["Package"]</label>
            <input type="text" class="form-control form-input" id="add-so-line-details-package-search-select"/>
            <input type="hidden" id="add-so-line-details-package-search-select-value" name="PackageNo"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-date-promised" class="form-label" >@_localizer["Date Promised"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" class="form-control form-input datepicker" style="background: #fafaf4; cursor: pointer;" 
                    placeholder="@_localizer["DD/MM/YYYY"]" 
                    id="add-so-line-details-date-promised" 
                    name="DatePromised"
                    readonly>
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-date-required-input" class="form-label" >@_localizer["Date Required"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" class="form-control form-input datepicker" style="background: #fafaf4; cursor: pointer;" 
                    placeholder="@_localizer["DD/MM/YYYY"]" 
                    id="add-so-line-details-date-required-input" 
                    name="DateRequired"
                    readonly>
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-ship-asap" class="form-label">@_localizer["Ship ASAP"]</label>
            <input type="checkbox" class="form-control form-check-input" id="add-so-line-details-ship-asap" name="shipAsap">
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-product-source" class="form-label">@_localizer["Product Source"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <select id="add-so-line-details-product-source" class="dropdown" name="ProductSource">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-shipping-instructions" class="form-label">@_localizer["Shipping Instr"]</label>
            <textarea class="form-control form-textarea height-auto" style="height:auto" 
                id="add-so-line-details-shipping-instructions" 
                data-directive="maxLength" 
                name="ShippingInstructions"
                maxlength="2000" rows="2"></textarea>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-printed-notes" class="form-label">@_localizer["Notes"]</label>
            <textarea class="form-control form-textarea height-auto" style="height:auto" 
                id="add-so-line-details-printed-notes" 
                data-directive="maxLength" 
                name="LineNotes"
                maxlength="2000" rows="2"></textarea>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-po-delivery-date" class="form-label" >@_localizer["PO Delivery Date"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" class="form-control form-input datepicker" style="background: #fafaf4; cursor: pointer;" 
                    placeholder="@_localizer["DD/MM/YYYY"]" 
                    id="add-so-line-details-po-delivery-date" 
                    name="PoDeliveryDate"
                    readonly>
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-contract-no" class="form-label">@_localizer["Contract No"]</label>
            <input type="text" class="form-control form-input" id="add-so-line-details-contract-no" name="ContractNo" maxlength="50">
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-msl" class="form-label">@_localizer["MSL"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <select id="add-so-line-details-msl" class="dropdown" name="Msl">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="add-so-line-details-print-hazardous-warning" class="form-label">@_localizer["Print hazardous warning"]</label>
            <input type="checkbox" class="form-control form-check-input" id="add-so-line-details-print-hazardous-warning" name="PrintHazardousWarning">
        </div>
</form>

<script>
    const addSoLineMessages = {
        overCreditLimitOfCompanyMessage: '@_messageLocalizer["The total of open orders takes the company over its credit limit, this Sales Order cannot be authorised"]',
    };
</script>