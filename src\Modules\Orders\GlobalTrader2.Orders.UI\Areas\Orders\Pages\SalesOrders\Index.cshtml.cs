using GlobalTrader2.Dto.DataListNugget;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.UserAccount.UseCases.FilterState.Queries.GetFilterStates;
using Microsoft.AspNetCore.Http;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders
{

    [SectionAuthorize(SecurityFunction.OrdersSection_View)]
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly IMediator _mediator;
        public IndexModel(SessionManager sessionManager, SecurityManager securityManager, IMediator mediator, IHttpContextAccessor httpContextAccessor) : base(securityManager)
        {
            _mediator = mediator;
            _sessionManager = sessionManager;

            AddBreadCrumbs();
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_SalesOrderBrowse;
        }
        public DataListNuggetState<SalesOrdersFilter> DataListNuggetState { get; set; } = new DataListNuggetState<SalesOrdersFilter>();
        public ViewLevelList CurrentTab { get; set; } = 0;
        public SalesOrdersFilterConfig SalesOrdersFilterConfig { get; set; } = new SalesOrdersFilterConfig();

        public string PageTitle { get; set; } = "Sales Orders";
        public bool CanAddSalesOrders { get; set; }
        public async Task<IActionResult> OnGetAsync()
        {
            var getTabResult = await GetTabAsync();
            if (!getTabResult) return Redirect(V2Paths.NotFound);

            int loginId = _sessionManager.GetInt32(SessionKey.LoginID).GetValueOrDefault();
            DataListNuggetState = await GetDataListNuggetState((int)DataListNuggets.SalesOrders, string.Empty, loginId);
            SalesOrdersFilterConfig = new SalesOrdersFilterConfig
            {
                InitSaveDataListState = HttpContext.Session.Get<bool>(SessionKey.SaveDataListNuggetStateByDefault),
                IsGlobalLogin = _sessionManager.IsGlobalUser,
                IsGSA = _sessionManager.IsGSA,
                InitViewLevel = (int)CurrentTab,
                DefaultSortDirection = DataListNuggetState.SortDirection,
                DefaultSortIndex = DataListNuggetState.SortIndex + 1
            };
            SetupPermissions();
            return Page();
        }

        private async Task<bool> GetTabAsync()
        {
            var visibleTabs = new List<int>();
            visibleTabs = await GetVisibleTabSecurityList(SecurityFunction.Orders_SalesOrders_View);
            if (!visibleTabs.Exists(x => x == (int)ViewLevelList.My)) return false;
            var defaultPage = _sessionManager.DefaultListPageView;
            CurrentTab = visibleTabs.Exists(t => t == (int)defaultPage) ? defaultPage : ViewLevelList.My;

            VisibleTab = Enum.GetValues(typeof(ViewLevelList))
                .Cast<ViewLevelList>()
                .Where(e => visibleTabs.Contains((int)e))
                .ToList();

            return true;
        }
        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.SalesOrders);

            BreadCrumbMenu.Add(BreadCrumbHelper.Order_MenuNew(_securityManager!));
        }

        private async Task<DataListNuggetState<SalesOrdersFilter>> GetDataListNuggetState(int intDataListNuggetID, string strDataListNuggetSubType, int userId)
        {
            var dataListNuggetState = await _mediator.Send(new GetFilterStatesQuery
            {
                DataListNuggetNo = intDataListNuggetID,
                LoginNo = userId,
                SubType = strDataListNuggetSubType,
            });

            if (dataListNuggetState.Success && dataListNuggetState.Data != null)
            {
                return DataListNuggetState<SalesOrdersFilter>.Deserialize(dataListNuggetState.Data.StateText);
            }

            return new DataListNuggetState<SalesOrdersFilter>();
        }
        private void SetupPermissions()
        {
            CanAddSalesOrders = _securityManager!.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_Add);
        }
    }
}
