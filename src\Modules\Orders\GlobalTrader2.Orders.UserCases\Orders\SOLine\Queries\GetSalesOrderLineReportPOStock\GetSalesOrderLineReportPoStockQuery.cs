﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPOStock
{
    public class GetSalesOrderLineReportPoStockQuery : IRequest<BaseResponse<IReadOnlyList<SalesOrderLineReportPoStockDto>>>
    {
        public int SalesOrderLineId { get; set; }
        public GetSalesOrderLineReportPoStockQuery(int SalesOrderLineId)
        {
            this.SalesOrderLineId = SalesOrderLineId;
        }
    }
}
