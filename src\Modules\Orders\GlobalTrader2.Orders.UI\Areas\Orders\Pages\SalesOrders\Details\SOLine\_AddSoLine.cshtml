﻿@using GlobalTrader2.Dto.SalesOrderLine
@using GlobalTrader2.SharedUI.Enums
@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.SharedUI.Services
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer
@inject IWebResourceManager _webResourceManager;
@inject SecurityManager _securityManager;
@inject SessionManager _sessionManager;

@model LinesSectionViewModel

<div id="add-lines-dialog" title="@_commonLocalizer["Lines"]" style="display:none" class="fs-12" data-default-page-size="@Model.DefaultListPageSize">
    <div class="dialog-description mb-2">
        <div class="d-flex justify-content-between">
            <div class="text-uppercase">
                <h5 class="text-uppercase">@_commonLocalizer["Add Sales Order Line"]</h5>
            </div>
        </div>
        <div class="line"></div>
        <div id="add-lines-stepper"></div>
    </div>
    <div class="stepper-content">
        <div class="step-pane step-pane-1">
            @await Html.PartialAsync("SOLine/_AddSoLine_Step1", Model)
        </div>
        <div class="step-pane step-pane-2 d-none">
            @await Html.PartialAsync("SOLine/_AddSoLine_Step2")
        </div>
        <div class="step-pane step-pane-3 d-none">
            @await Html.PartialAsync("SOLine/_AddSoLine_Step3")
        </div>
        <div class="step-pane step-pane-4 d-none">
            @await Html.PartialAsync("SOLine/_AddSoLine_Step4")
        </div>
    </div>
</div>