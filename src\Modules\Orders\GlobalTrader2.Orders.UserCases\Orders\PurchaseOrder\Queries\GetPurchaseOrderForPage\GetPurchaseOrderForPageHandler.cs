﻿using GlobalTrader2.Dto.PurchaseOrder;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderForPage
{
    public class GetPurchaseOrderForPageHandler : IRequestHandler<GetPurchaseOrderForPageQuery, BaseResponse<PurchaseOrderForPageDto>>
    {
        private readonly IBaseRepository<PurchaseOrderForPageReadModel> _purchaseOrderForSelectRepository;
        private readonly IMapper _mapper;
        public GetPurchaseOrderForPageHandler(IBaseRepository<PurchaseOrderForPageReadModel> purchaseOrderForSelectRepository, IMapper mapper)
        {
            _purchaseOrderForSelectRepository = purchaseOrderForSelectRepository;
            _mapper = mapper;
        }
        public async Task<BaseResponse<PurchaseOrderForPageDto>> Handle(GetPurchaseOrderForPageQuery request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@PurchaseOrderId", SqlDbType.Int) { Value = request.PurchaseOrderId }
            };
            var result = await _purchaseOrderForSelectRepository.SqlQueryRawAsync($"{StoredProcedures.Select_PurchaseOrder_for_Page} @PurchaseOrderId", parameters.ToArray());
            if (result != null && result.Any())
            {
                return new BaseResponse<PurchaseOrderForPageDto>
                {
                    Success = true,
                    Data = _mapper.Map<PurchaseOrderForPageDto>(result[0])
                };
            }
            return new BaseResponse<PurchaseOrderForPageDto>();
        }
    }
}
