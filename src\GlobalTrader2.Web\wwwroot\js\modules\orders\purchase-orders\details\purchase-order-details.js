﻿import { PurchaseOrderMainInfoManager } from './components/main-info/main-info.js?v=#{BuildVersion}#';

$(async () => {
    const purchaseOrderDetailsManager = new PurchaseOrderDetailsManager();
    purchaseOrderDetailsManager.initialize();
});
class PurchaseOrderDetailsManager {
    constructor() {
        this.purchaseOrderId = getParameterByName('po');
        this.purchaseOrderMainInfoManager = new PurchaseOrderMainInfoManager(this.purchaseOrderId);
    }

    async initialize() {
        await this.purchaseOrderMainInfoManager.initialize();
    }
}