﻿using GlobalTrader2.Core.Constants;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries.Dtos;
using GlobalTrader2.UserAccount.UseCases.RecentlyViewed.Commands.Create;
using GlobalTrader2.SharedUI.Services;
using Microsoft.AspNetCore.Http;
using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.DocumentsSection;
using GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details;
using GlobalTrader2.SharedUI;
using Microsoft.Extensions.Localization;


namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Details
{
    public class GsaInfo
    {
        public bool IsGSAUser { get; set; }
        public bool IsDifferentClient { get; set; }
        public bool BelongToGSA { get; set; }
        public bool HasEditPermission { get; set; }
        public bool IsDMCC { get; set; }
        public string? ClientNameForDisplay { get; set; }
        public bool ShowClientNamePanel { get; set; }
    }

    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly IMediator _mediator;
        private readonly IStringLocalizer<Misc> _miscLocalizer;
        public IndexModel(SecurityManager securityManager, IMediator mediator, SessionManager sessionManager, IStringLocalizer<Misc> miscLocalizer) : base(securityManager)
        {
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_BOMDetail;
            _sessionManager = sessionManager;
            _mediator = mediator;
            _miscLocalizer = miscLocalizer;
        }

        [FromQuery(Name = "bom")]
        public int? BomId { get; set; }
        public bool CanAddSourcingResult { get; set; }
        public bool CanEditSourcingResult { get; set; }
        public bool CanQuoteSourcingResult { get; set; }
        public bool CanDeleteSourcingResult { get; set; }

        public bool CanImportExportSourcingResult { get; set; }
        public bool IsPOHub => _sessionManager.IsPOHub;
        public GsaInfo Gsa { get; set; } = new GsaInfo();
        public string BomStatusText { get; set; } = string.Empty;
        public bool ShowExportPurchaseHubButton { get; set; }
        public bool ShowEditBomButton { get; set; } = true;
        public bool ShowDeleteBomButton { get; set; } = true;
        public bool ShowExportCsvButton { get; set; } = true;
        public bool ShowReleaseBomButton { get; set; } = true;
        public bool ShowCloseBomButton { get; set; } = true;
        public bool ShowNoBidBomButton { get; set; } = true;
        public bool ShowRecallNoBidBomButton { get; set; } = true;
        public bool ShowNotifyButton { get; set; } = true;
        public bool ShowAddNoteButton { get; set; } = true;
        public bool ShowViewTreeButton { get; set; } = true;
        public bool ShowCrossMatchButton { get; set; }
        public bool ShowApplyPartWatchButton { get; set; }
        public bool CanViewPPVQualification { get; set; }
        public BomForPageDto? BomDetails { get; set; }
        public bool IsShowPPVQualificationButton { get; set; } = true;
        public bool CanAddHubrfqItem { get; set; } = true;


        public DocumentsSectionViewModel DropDragImageDocumentsViewModel { get; set; } = new();

        public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
        {
            if (!BomId.HasValue)
                return NotFound();

            // Load BOM details
            var bomDetails = await _mediator.Send(new GetBomForPageQuery { BomId = BomId.Value }, cancellationToken);
            if (bomDetails?.Data == null)
                return NotFound();

            BomDetails = bomDetails.Data;

            var isPoHub = _sessionManager.IsPOHub;
            Gsa.IsGSAUser = _sessionManager.IsGSA;
            Gsa.HasEditPermission = !_sessionManager.IsGSAViewPermission;
            Gsa.IsDMCC = _sessionManager.ClientID == ClientId.DMCC;

            var canView = true;

            if (BomDetails.ClientNo != _sessionManager.ClientID)
            {
                canView = false;
                if ((isPoHub && BomDetails.RequestToPOHubBy > 0) || Gsa.IsGSAUser)
                {
                    canView = true;
                }
            }

            if (!canView)
            {
                return NotFound();
            }

            // Set default button visibility
            SetButtonVisibility(isPoHub);

            var clientId = _sessionManager.ClientID;
            Gsa.IsDifferentClient = BomDetails.ClientNo != clientId;
            Gsa.BelongToGSA = BomDetails.ClientNo == ClientId.DMCC;
            BomStatusText = BomDetails.Status;

            // GSA user viewing other client's BOM (except GSA itself)
            if (Gsa.IsGSAUser && Gsa.IsDifferentClient && !Gsa.IsDMCC)
            {
                Gsa.ClientNameForDisplay = BomDetails.ClientName;
                Gsa.ShowClientNamePanel = true;

                if (!Gsa.HasEditPermission)
                    DisableAllActionButtons();
            }
            await SetupPermissions();
            AddBreadCrumbs();
            await UpdateRecentlyView();

            SetupDropDragImageDocumentsSectionBox();

            return Page();
        }

        // Loading perrmission of Setup section level
        private async Task SetupPermissions()
        {
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);

            if (!loginId.HasValue) return;

            await _securityManager!.LoadSectionLevelPermissions(loginId.Value, (int)SiteSection.Setup, false);
            CanViewPPVQualification = _securityManager!.CheckSectionLevelPermission(SecurityFunction.Setup_GlobalSettings_PPVBOMQualification);
            CanImportExportSourcingResult = _securityManager!.CheckSectionLevelPermission(SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult);
        }

        private void SetButtonVisibility(bool isPoHub)
        {
            ShowExportPurchaseHubButton = !isPoHub;
            ShowReleaseBomButton = isPoHub;
            ShowCloseBomButton = !isPoHub;
            ShowNoBidBomButton = isPoHub;
            ShowRecallNoBidBomButton = isPoHub;
            ShowCrossMatchButton = isPoHub;

            ShowEditBomButton = true;
            ShowDeleteBomButton = true;
            ShowExportCsvButton = true;
            ShowNotifyButton = true;
            ShowAddNoteButton = true;
            ShowApplyPartWatchButton = isPoHub;
            IsShowPPVQualificationButton = true;
            CanAddHubrfqItem = !(isPoHub || (Gsa.IsDifferentClient && _sessionManager.IsGSA && !_sessionManager.IsGlobalUser));
        }

        private void DisableAllActionButtons()
        {
            ShowEditBomButton = false;
            ShowDeleteBomButton = false;
            ShowExportPurchaseHubButton = false;
            ShowReleaseBomButton = false;
            ShowCloseBomButton = false;
            ShowNoBidBomButton = false;
            ShowRecallNoBidBomButton = false;
            ShowCrossMatchButton = false;

            ShowExportCsvButton = false;
            ShowNotifyButton = false;
            ShowAddNoteButton = false;
            ShowApplyPartWatchButton = false;
            IsShowPPVQualificationButton = _sessionManager.IsGlobalUser;
        }

        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.BOMBrowse);
            BreadCrumb.Add(Navigations.BOMDetail(BomDetails != null ? BomDetails.BOMName : ""));
        }

        private async Task UpdateRecentlyView()
        {
            var lastBreadcrumb = BreadCrumb[^1];
            var command = new CreateRecentlyViewedCommand
            {
                LoginNo = HttpContext.Session.GetInt32(SessionKey.LoginID),
                PageTitle = lastBreadcrumb.PageTitle,
                PageUrl = $"{lastBreadcrumb.CtaUri}{HttpContext.Request.QueryString.Value}"
            };
            await _mediator.Send(command);
        }

        private void SetupDropDragImageDocumentsSectionBox()
        {
            if (_securityManager == null) return;
            DropDragImageDocumentsViewModel = new()
            {
                SectionId = "BOM-image-documents-drop-drag-id",
                DocumentType = DocumentSizeByType.ImageDocument,
                SectionName = _miscLocalizer["QCDocument", BomId!, BomId!],
                IsReadOnly = false,
                CanAdd = _sessionManager.IsPOHub,
                CanDelete = _sessionManager.IsPOHub,
                NoDataMessage = _miscLocalizer["This Sourcing Result has no images attached"],
                SectionBoxTitle = _miscLocalizer["Images Attached"],
                UploadDialog = new()
                {
                    DialogId = "BOM-image-documents-drop-drag-upload-dialog",
                    FormId = "BOM-image-documents-drop-drag-upload-form",
                    SectionName = "BOM-image-documents-drop-drag-upload-section",
                    DialogTitle = _miscLocalizer["Images Attached"],
                    DialogName = _miscLocalizer["Add New Images"],
                    DialogDescription = _miscLocalizer["Select a new image(s) and press Save"],
                    AllowedFileExtensions = _miscLocalizer["jpg, jpeg, png, gif"]
                },
                RemoveFileDialog = new()
                {
                    DialogId = "BOM-image-documents-drop-drag-remove-dialog",
                    SectionName = "BOM-image-documents-drop-drag-remove-section",
                    DialogTitle = _miscLocalizer["Delete Images Attached"],
                    DialogDescription = _miscLocalizer["Are you sure you would like to delete this Image(s)?"],
                }
            };
        }
    }
}
