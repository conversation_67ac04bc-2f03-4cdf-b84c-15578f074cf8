﻿namespace GlobalTrader2.Core.Domain.Entities
{
    public class ServiceItemDetailReadModel
    {
        public int ServiceId { get; set; }
        public int ClientNo { get; set; }
        public string ServiceName { get; set; } = string.Empty;
        public string ServiceDescription { get; set; } = string.Empty;
        public double Price { get; set; }
        public double Cost { get; set; }
        public string? Notes { get; set; }
        public int? LotNo { get; set; }
        public bool Inactive { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime DLUP { get; set; }
        public string? RefIdHK { get; set; }
        public string? LotName { get; set; }
        public int Allocations { get; set; }
    }
}
