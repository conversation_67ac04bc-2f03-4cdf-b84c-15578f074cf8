﻿using GlobalTrader2.Core.Enums;
using GlobalTrader2.SharedUI.Constants;
using GlobalTrader2.SharedUI.Models;

namespace GlobalTrader2.SharedUI.Helper
{
    public static class RedirectUrlHelper
    {
        private const string UrlFormat = "{0}{1}";
        public static string GetUrlWithParams_CompanyDetail(CompanyListType? companyListType, ViewLevelList? tab, int? companyId)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                clt = (int?)companyListType,
                tab = (int?)tab,
                cm = companyId,
            };

            return string.Format(UrlFormat, Navigations.CompanyDetails(string.Empty).CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_CompanyList(CompanyListType? companyListType)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                clt = (int?)companyListType,
            };

            return string.Format(UrlFormat, Navigations.AllCompanies.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_PurchaseOrderAdd(int? companyId, string? companyName, int? contactId)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId
            };

            return string.Format(UrlFormat, Navigations.PurchaseOrdersAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_CustomerRequirementAdd(int? companyId, string? companyName, int? contactId)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId
            };

            return string.Format(UrlFormat, Navigations.CustomerRequirementAdd.CtaUri, queryParams.ToQueryString());
        }
        public static string GetUrlWithParams_QuoteAdd(int? companyId, string? companyName, int? contactId)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId
            };

            return string.Format(UrlFormat, Navigations.QuoteOrderAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_SalesOrderAdd(int? companyId, string? companyName, int? contactId, string? searchCompanyName)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId,
                scn = searchCompanyName
            };

            return string.Format(UrlFormat, Navigations.SalesOrderAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_InvoicesAdd(int? companyId, string? companyName, string? contactName, int? contactId)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                ctn = contactName,
                con = contactId,
            };

            return string.Format(UrlFormat, Navigations.InvoicesAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_SupplierInvoicesAdd(int? companyId, string? companyName, int? goodsInId)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                gi = goodsInId
            };

            return string.Format(UrlFormat, Navigations.SupplierInvoiceAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_CustomerRMAAdd(int? companyId, string? companyName, int? contactId, string? contactName)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId,
                ctn = contactName,
            };

            return string.Format(UrlFormat, Navigations.CustomerRMAAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_SupplierRMAAdd(int? companyId, string? companyName, int? contactId, string? contactName)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId,
                ctn = contactName,
            };

            return string.Format(UrlFormat, Navigations.SupplierRMAAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_CreditNoteAdd(int? companyId, string? companyName, int? contactId, string? contactName)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId,
                ctn = contactName,
            };

            return string.Format(UrlFormat, Navigations.CreditNoteAdd.CtaUri, queryParams.ToQueryString());
        }
        public static string GetUrlWithParams_DebitNoteAdd(int? companyId, string? companyName, int? contactId, string? contactName)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                cm = companyId,
                cmn = companyName,
                con = contactId,
                ctn = contactName,
            };

            return string.Format(UrlFormat, Navigations.DebitNoteAdd.CtaUri, queryParams.ToQueryString());
        }

        public static string GetUrlWithParams_SalesOrder(int salesOrderId)
        {
            var queryParams = new NavigationCompanyDetailParams()
            {
                so = salesOrderId
            };
            return string.Format(UrlFormat, Navigations.SalesOrderDetail("").CtaUri, queryParams.ToQueryString());
        }
        public static string GetUrlWithoutParams_PurchaseOrder()
        {
            return Navigations.PurchaseOrderDetail("").CtaUri;
        }
        public static string GetUrlWithoutParams_InternalPurchaseOrder()
        {

            return Navigations.InternalPurchaseOrderDetail.CtaUri;
        }
    }
}
