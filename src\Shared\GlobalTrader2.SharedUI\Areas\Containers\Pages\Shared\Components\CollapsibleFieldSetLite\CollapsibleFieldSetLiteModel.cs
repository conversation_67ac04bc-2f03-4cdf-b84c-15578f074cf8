﻿namespace GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.CollapsibleFieldSetLite
{
    public class CollapsibleFieldSetLiteModel
    {
        public string CollapsibleId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string ViewComponentName { get; set; } = string.Empty;
        public string? BorderColor { get; set; } = "#d4ccb0";
        public string? BackgroundColor { get; set; } = "none";
        public bool IsOpen { get; set; } //collapsed when first init
        public bool IsShow { get; set; } //show when first init
        public CommonComponentParams? ViewComponentParams { get; set; }
    }

    public class CommonComponentParams
    {
        public string ColumnColor { get; set; } = string.Empty;
        public string HeaderTextColor { get; set; } = string.Empty;
        public bool? CanAdd { get; set; }
        public bool? CanDeallocate { get; set; }
    }
}
