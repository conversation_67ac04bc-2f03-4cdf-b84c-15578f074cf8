﻿import { CollapsibleBoxHelper } from '../../../../../../../helper/collapsible-box-helper.js';

export class AllocationsManager {
    constructor(salesOrderLineId) {
        this.salesOrderLineId = salesOrderLineId;

        this.$container = $('#allocations-details-container');
        this.$refreshButton = this.$container.find('.collapsible-box-refresh-button');

        this.allocationsData = [];
        this.$allocationsTable = null;
        this.allocationsEndpoint = () => `/orders/sales-order/so-lines/${this.salesOrderLineId}/allocations`;
    }

    async initialize() {
        this._setupButtonBehaviours();
        await this._initTable();
    }

    async _initTable() {
        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$container, true);
        await this.getAllocationsAsync();
        this.$allocationsTable = $('#allocations-table')
            .DataTable({
                data: this.allocationsData ?? [],
                info: false,
                scrollCollapse: true,
                responsive: true,
                searching: false,
                paging: false,
                ordering: false,
                lengthChange: false,
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                },
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                columns: [
                    {
                        className: 'text-wrap text-break',
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Supplier Part`,
                        data: 'part',
                        render: (data, type, row) => {
                            const content = data; // Has special format
                            return `
                            <a href="${GlobalTrader.PageUrlHelper.Get_URL_Stock(row.stockNo)}" class="dt-hyper-link" style="min-height: 15px;">${content}</a>
                            <p class="m-0" style="min-height: 15px;">${content}</p>
                            `;
                        }
                    },
                    {
                        className: 'text-wrap text-break',
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Supplier Part`,
                        data: 'part',
                        render: (data, type, row) => {
                            let companyInfo = {
                                href: '',
                                name: '',
                                advisoryNoteHtml: GlobalTrader.HtmlHelper.createAdvisoryHtml(
                                    {
                                        advisory: GlobalTrader.StringHelper.formatAdvisoryNotes(row.companyAdvisoryNotes),
                                        options: {
                                            class: "ms-1",
                                            style: "margin-top: -3px"
                                        }
                                    }
                                )
                            };

                            if (row.ipoSupplier > 0) {
                                companyInfo.href = GlobalTrader.PageUrlHelper.Get_URL_Company(row.ipoSupplier)
                                companyInfo.name = row.ipoSupplierName;
                            } else {
                                companyInfo.href = GlobalTrader.PageUrlHelper.Get_URL_Company(row.companyNo);
                                companyInfo.name = row.companyName;
                            }

                            return `
                                <div class="m-0" style="min-height: 15px;">
                                    <a class="dt-hyper-link" href="${companyInfo.href}">
                                        ${companyInfo.name} 
                                    </a> 
                                    <span>${companyInfo.advisoryNoteHtml}</span>
                                </div>
                                <div class="d-flex justify-content-between" style="min-height: 15px;">
                                    <span></span>
                                    <span></span>
                                </div>
                            `;
                        }
                    },
                    {
                        className: 'text-wrap text-break',
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Supplier Part`,
                        data: (row) => (
                            {
                                quantityInStock: row.quantityInStock,
                                quantityOnOrder: row.quantityOnOrder,
                            }
                        ),
                        render: (data, type, row) => {
                            return `<p class="m-0" style="min-height: 15px;">${data.quantityInStock}</p><p class="m-0" style="min-height: 15px;">${data.quantityOnOrder}</p>`;
                        }
                    },
                    //{
                    //    className: 'text-wrap text-break',
                    //    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Supplier Part`,
                    //    data: (row) => (
                    //        {
                    //            quantityInStock: row.quantityInStock,
                    //            quantityOnOrder: row.quantityOnOrder,
                    //        }
                    //    ),
                    //    render: (data, type, row) => {
                    //        return `<p class="m-0" style="min-height: 15px;">${data.quantityInStock}</p><p class="m-0" style="min-height: 15px;">${data.quantityOnOrder}</p>`;
                    //    }
                    //},

                ],
                rowId: 'companyId', //
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                }
            })
        CollapsibleBoxHelper.updateRowsCount(this.$container, this.allocationsData.length);
        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$container, false);
    }

    async getAllocationsAsync() {
        const result = await GlobalTrader.ApiClient.getAsync(this.allocationsEndpoint());
        if (result.success) {
            this.allocationsData = result.data;
        }
    }
    async reloadSection(salesOrderLineId) {
        if (!salesOrderLineId) return;

        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$container, true);
        CollapsibleBoxHelper.updateRowsCount(this.$container, 0);

        this.salesOrderLineId = salesOrderLineId;
        await this.getAllocationsAsync();
        this.$allocationsTable.clear().draw();
        this.$allocationsTable.rows.add(this.allocationsData).draw();

        CollapsibleBoxHelper.updateRowsCount(this.$container, this.allocationsData.length);
        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$container, false);
    }

    showSection(isShow = true) {
        if (isShow) {
            this.$container.show();
        } else {
            this.$container.hide();
        }
    }

    _setupButtonBehaviours() {
        this.$refreshButton.button().on("click", async () => {
            this.reloadSection();
        });
    }

   
}

