﻿using GlobalTrader2.Core.Enums;
using System.Globalization;
using System.Text.RegularExpressions;

namespace GlobalTrader2.Core.Helpers
{
    public static partial class Functions
    {
        public static string FormatNumeric(object? amount, CultureInfo ci) { return FormatNumeric(amount, 0, false, ci); }
        public static string FormatNumeric(object? amount, int figures, CultureInfo ci) { return FormatNumeric(amount, figures, false, ci); }
        public static string FormatNumeric(object? objAmount, int figures, bool hasCommaThousands, CultureInfo ci)
        {
            if (objAmount == null) objAmount = 0;
            Double dbl = Convert.ToDouble(objAmount);
            return dbl.ToString(string.Format("{0}{1}", (hasCommaThousands) ? "n" : "f", figures), ci);
        }
        public static string FormatDate(DateTime? date, CultureInfo ci) { return FormatDate(date, false, false, false, ci); }
        public static string FormatDate(DateTime? date, bool isLongDate, bool includeTime, CultureInfo ci) { return FormatDate(date, isLongDate, includeTime, false, ci); }
        public static string FormatDate(DateTime? date, bool isLongDate, bool includeTime, bool islongTime, CultureInfo ci)
        {
            string strReturn = "";
            if (date != null)
            {
                DateTime date2 = (DateTime)date;
                if (date2.Year == 1 && date2.Month == 1 && date2.Day == 1)
                {
                    strReturn = "";
                }
                else
                {
                    strReturn = date2.ToString(isLongDate ? "dd MMMM yyyy" : "dd/MM/yyyy", ci);
                    if (includeTime) strReturn += string.Format(" {0}", date2.ToString(islongTime ? "HH:mm:ss" : "HH:mm", ci));
                }
            }
            return strReturn;
        }
        public static string FormatDate(DateTime? date)
        {
            if (!date.HasValue || date.Value.Year == 1) return string.Empty;
            return date.Value.ToString("dd/MM/yyyy");
        }

        public static string FormatTime(DateTime? date, CultureInfo ci)
        {
            string strReturn = "&nbsp;";
            if (date != null)
            {
                DateTime date2 = (DateTime)date;
                strReturn = date2.ToString("t", ci);
            }
            return strReturn;
        }
        public static string FormatDateTime(DateTime? date, string formatString, CultureInfo ci, bool uppercaseTimeDesignator = false)
        {
            if (!date.HasValue)
            {
                return string.Empty;
            }

            if (uppercaseTimeDesignator)
            {
                DateTimeFormatInfo datetimeFormatInfo = (DateTimeFormatInfo)ci.DateTimeFormat.Clone();
                datetimeFormatInfo.AMDesignator = "AM";
                datetimeFormatInfo.PMDesignator = "PM";

                return date.Value.ToString(formatString, datetimeFormatInfo);
            }

            return date.Value.ToString(formatString, ci);
        }

        #region Format Currency
        public static string FormatCurrency(object? objAmount, CultureInfo cultureInfo, string? strCurrency, int intFigures, bool blnCommaThousands)
        {
            objAmount ??= 0;
            double dbl = Convert.ToDouble(objAmount);
            string strReturn = dbl.ToString(string.Format("{0}{1}", blnCommaThousands ? "n" : "f", intFigures), cultureInfo);
            if (!string.IsNullOrEmpty(strCurrency)) strReturn += string.Format(" {0}", strCurrency.Trim());
            return strReturn.Trim();
        }
        public static string FormatCurrency(decimal? objAmount, CultureInfo cultureInfo, string? strCurrency, int intFigures, bool blnCommaThousands)
        {
            objAmount ??= 0;
            decimal dbl = Convert.ToDecimal(objAmount);
            string strReturn = dbl.ToString(string.Format("{0}{1}", blnCommaThousands ? "n" : "f", intFigures), cultureInfo);
            if (!string.IsNullOrEmpty(strCurrency)) strReturn += string.Format(" {0}", strCurrency.Trim());
            return strReturn.Trim();
        }
        public static string FormatCurrency(double? amount, string? currencyCode)
        {
            var curencyFigures = 5;
            if (!amount.HasValue)
            {
                return string.Empty;
            }
            return FormatCurrency(amount.Value, CultureInfo.CurrentCulture, currencyCode, curencyFigures, false);
        }
        public static string FormatCurrencyForReport(object objAmount, CultureInfo cultureInfo, int intFigure)
        {
            return FormatCurrency(objAmount, cultureInfo, "", intFigure, true);
        }
        public static string FormatCurrencyForReport(object objAmount, CultureInfo cultureInfo, string strCurrency)
        {
            return FormatCurrency(objAmount, cultureInfo, strCurrency, 5, true);
        }
        public static string FormatCurrencyForReport(object objAmount, CultureInfo cultureInfo)
        {
            return FormatCurrency(objAmount, cultureInfo, "", 5, true);
        }
        /// <summary>
        /// Use for decimal type
        /// </summary>
        public static string FormatDecimalCurrency(object objAmount, CultureInfo cultureInfo, string strCurrency, int intFigures, bool blnCommaThousands)
        {
            objAmount ??= 0;
            decimal dbl = Convert.ToDecimal(objAmount);
            string strReturn = dbl.ToString(string.Format("{0}{1}", blnCommaThousands ? "n" : "f", intFigures), cultureInfo);
            if (!string.IsNullOrEmpty(strCurrency)) strReturn += string.Format(" {0}", strCurrency.Trim());
            return strReturn.Trim();
        }

        /// <summary>
        /// Formats a Currency string with two values, first currency passed comes first
        /// e.g. 100.00 USD (90.00 GBP)
        /// </summary>
        public static string FormatConvertedCurrency(
            double? amount1,
            CultureInfo cultureInfo,
            int? currencyNo1,
            string currencyCode1,
            double? amount2,
            int? currencyNo2,
            string currencyCode2,
            int? decimalPlaces)
        {
            if (amount1 == null) amount1 = 0;
            if (amount2 == null) amount2 = 0;
            if (decimalPlaces == null) decimalPlaces = 2;
            if (currencyNo1 == currencyNo2 || amount1.Equals(0.0) || currencyNo1 == null || currencyNo1 == 0)
            {
                return Functions.FormatCurrency((double)amount1, cultureInfo, currencyCode1, (int)decimalPlaces, true);
            }
            else
            {
                return String.Format(
                    "{0} ({1})",
                    Functions.FormatCurrency((double)amount1, cultureInfo, currencyCode1, (int)decimalPlaces, true),
                    Functions.FormatCurrency((double)amount2, cultureInfo, currencyCode2, (int)decimalPlaces, true)
                 );
            }
        }
        /// <summary>
        /// for decimal type
        /// Formats a Currency string with two values, first currency passed comes first
        /// e.g. 100.00 USD (90.00 GBP)
        /// </summary>
        /// <param name="amount1"></param>
        /// <param name="cultureInfo"></param>
        /// <param name="currencyNo1"></param>
        /// <param name="currencyCode1"></param>
        /// <param name="amount2"></param>
        /// <param name="currencyNo2"></param>
        /// <param name="currencyCode2"></param>
        /// <param name="decimalPlaces"></param>
        /// <returns></returns>
        public static string FormatConvertedCurrency(
           decimal? amount1,
           CultureInfo cultureInfo,
           int? currencyNo1,
           string currencyCode1,
           decimal? amount2,
           int? currencyNo2,
           string currencyCode2,
           int? decimalPlaces)
        {
            if (amount1 == null) amount1 = 0;
            if (amount2 == null) amount2 = 0;
            if (decimalPlaces == null) decimalPlaces = 2;
            if (currencyNo1 == currencyNo2 || amount1.Equals(0.0M) || currencyNo1 == null || currencyNo1 == 0)
            {
                return Functions.FormatCurrency(amount1, cultureInfo, currencyCode1, (int)decimalPlaces, true);
            }
            else
            {
                return String.Format(
                    "{0} ({1})",
                    Functions.FormatCurrency(amount1, cultureInfo, currencyCode1, (int)decimalPlaces, true),
                    Functions.FormatCurrency(amount2, cultureInfo, currencyCode2, (int)decimalPlaces, true)
                 );
            }
        }


        /// <summary>
        /// Formats a currency description string
        /// </summary>
        public static string FormatCurrencyDescription(string? strDescription, string? strCode)
        {
            string strReturn = "";
            if (!string.IsNullOrEmpty(strCode) && !string.IsNullOrEmpty(strDescription))
            {
                strReturn = string.Format("{0} ({1})", strDescription, strCode);
            }
            else
            {
                if (!string.IsNullOrEmpty(strCode)) strReturn = strCode;
                if (!string.IsNullOrEmpty(strDescription)) strReturn = strDescription;
            }
            return strReturn;
        }

        public static double ConvertValueFromBaseCurrency(double? dblValueToConvert, double dblRate)
        {
            double dblValueToConvert_AsDouble = (dblValueToConvert == null) ? 0 : (double)dblValueToConvert;
            return dblValueToConvert_AsDouble * dblRate;
        }
        public static decimal ConvertValueFromBaseCurrencyDecimal(decimal? dblValueToConvert, decimal dblRate)
        {
            decimal dblValueToConvert_AsDecimal = (dblValueToConvert == null) ? 0 : (decimal)dblValueToConvert;
            return dblValueToConvert_AsDecimal * dblRate;
        }

        public static double ConvertValueToBaseCurrency(double? dblValueToConvert, double dblRate)
        {
            double dblValueToConvert_AsDouble = !dblValueToConvert.HasValue ? 0 : dblValueToConvert.Value;
            return dblValueToConvert_AsDouble / dblRate;
        }
        public static decimal ConvertValueToBaseCurrencyDecimal(decimal? dblValueToConvert, decimal dblRate)
        {
            decimal dblValueToConvert_AsDouble = !dblValueToConvert.HasValue ? 0 : dblValueToConvert.Value;
            return dblValueToConvert_AsDouble / dblRate;
        }
        #endregion

        public static string ReplaceLineBreaks(string? strIn)
        {
            return ReplaceLineBreaks(strIn, "<br />");
        }

        public static string ReplaceLineBreaks(string? strIn, string strReplacement)
        {
            return (strIn ?? string.Empty)
            .Trim()
            .Replace("\r\n", strReplacement)
            .Replace("\r", strReplacement)
            .Replace("\n", strReplacement);
        }

        [GeneratedRegex("[^a-zA-Z0-9]")]
        private static partial Regex NonAlphanumericRegex();

        public static string RemoveNonAlphanumeric(string input)
        {
            return NonAlphanumericRegex().Replace(input, "");
        }

        public static string FormatURL(string? strURL)
        {
            if (string.IsNullOrEmpty(strURL))
                return string.Empty;

            strURL = strURL.Trim();

            if (!strURL.StartsWith("http", StringComparison.OrdinalIgnoreCase))
                return "http" + "://" + strURL;

            return strURL;
        }

        public static string FormatDateStaticBST(DateTime datetime, string? culture, bool longDate, bool includeTime, bool longTime = false)
        {
            string strReturn = "";

            if (datetime.Year != 1 || datetime.Month != 1 || datetime.Day != 1)
            {
                CultureInfo ci = new CultureInfo(culture ?? "en-GB");
                strReturn = datetime.ToString((longDate) ? "D" : "d", ci);
                if (includeTime) strReturn += string.Format(" {0}", datetime.ToString((longTime) ? "T" : "t", ci)) + " " + CheckDayLightSaving(datetime);

            }
            return strReturn;
        }

        public static string CheckDayLightSaving(DateTime ukTime)
        {
            TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById("GMT Standard Time");
            bool isDaylightSavingTime = timeZone.IsDaylightSavingTime(ukTime);
            string msg = isDaylightSavingTime ? "BST" : "UTC";
            return msg;
        }

        public static string FormatPercentage(object objAmount, int intFigures, bool blnAddPercentSign, CultureInfo cultrueInfo)
        {
            string strReturn = FormatNumeric(objAmount, intFigures, cultrueInfo);
            if (blnAddPercentSign) strReturn += "%";
            return strReturn;
        }

        public static string FormatPercentage(object objAmount, int intFigures, CultureInfo cultrueInfo)
        {
            return FormatPercentage(objAmount, intFigures, true, cultrueInfo);
        }

        public static string FormatPercentage(object objAmount, CultureInfo cultrueInfo)
        {
            return FormatPercentage(objAmount, 1, true, cultrueInfo);
        }

        public static string SetCleanTextValue(string strIn, bool isReplaceLineBreaks)
        {
            if (string.IsNullOrEmpty(strIn))
                strIn = string.Empty;

            strIn = strIn.Trim();

            strIn = strIn.Replace(":PLUS:", "+");
            strIn = strIn.Replace(":QUOTE:", "\"");
            strIn = Regex.Replace(strIn, "(:AND:)|(&amp;)", "&", RegexOptions.None, TimeSpan.FromSeconds(5));

            if (isReplaceLineBreaks)
                strIn = strIn.Replace("\n", "<br />");

            return strIn;
        }

        public static DateTime GetUKLocalTime()
        {

            TimeZoneInfo ukTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GMT Standard Time");
            DateTime currentUkTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, ukTimeZone);
            return currentUkTime;
        }

        [GeneratedRegex(@"[–—–]", RegexOptions.None)]
        private static partial Regex JunkCharRegex();
        public static string CleanJunkCharInCSV(string str)
        {
            str = JunkCharRegex().Replace(str, "-");
            return str;
        }

        public static string GetFullPart(string part)
        {
            return part?.Replace("-", "").Replace(" ", "").ToUpper() ?? string.Empty;
        }

        public static string BeautifyPartNumber(string partNumber)
        {
            partNumber = partNumber.Replace("_AMPERSAND_", "&");
            partNumber = partNumber.Replace("_HASH_", "#");
            partNumber = partNumber.Replace("_EQUALS_", "=");
            partNumber = partNumber.Replace("_PLUS_", "+");
            return partNumber;
        }
        public static string CleanHtmlWithLineBreak(string strIn)
        {
            if (string.IsNullOrEmpty(strIn))
                return string.Empty;
            strIn = Regex.Replace(strIn, @"<br\s*/?>", "\n", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(5));

            strIn = Regex.Replace(strIn, @"<\/?p\s*>", "", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(5));

            strIn = Regex.Replace(strIn, @"<\/?b[^>]*>", "", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(5));

            strIn = Regex.Replace(strIn, @"[ \t]+", " ", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(5));
            strIn = Regex.Replace(strIn, @"\s*\n\s*", "\n", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(5));
            strIn = strIn.Trim();

            return strIn;
        }
        public static string GetPartWithAlternate(string strPart, byte? intAltStatus, bool isAlternate)
        {
            if (!string.IsNullOrEmpty(strPart) && intAltStatus > 0 && isAlternate)
            {
                string alternateText = intAltStatus switch
                {
                    (byte)CustReqAlternative.Alternative => "Alternate",
                    (byte)CustReqAlternative.PossibleAlternative => "Possible Alternate",
                    (byte)CustReqAlternative.FirmAlternative => "Firm Alternate",
                    _ => "Alternate"
                };
                return $"{strPart} ({alternateText})";
            }
            return strPart ?? string.Empty;
        }
    }
}
