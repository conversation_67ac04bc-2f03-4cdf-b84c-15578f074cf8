﻿using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseRequisition;
using GlobalTrader2.Orders.UserCases.Helpers;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseRequisition.Queries
{
    public class GetSalesOrderLineHandler : IRequestHandler<GetSalesOrderLineQuery, BaseResponse<SalesOrderLineDto>>
    {
        private readonly IBaseRepository<SalesOrderLineModel> _salesOrderLineRepository;
        private readonly IBaseRepository<Login> _loginRepository;
        private readonly ISender _sender;
        private readonly IBaseRepository<ManufacturerAdvisoryNote> _manuAdvRepository;
        private readonly IMapper _mapper;
        public GetSalesOrderLineHandler(IBaseRepository<SalesOrderLineModel> salesOrderLineRepository
            , IBaseRepository<Login> loginRepository
            , IMapper mapper
            , IBaseRepository<ManufacturerAdvisoryNote> manuAdvRepository
            , ISender sender)
        {
            _salesOrderLineRepository = salesOrderLineRepository;
            _loginRepository = loginRepository;
            _sender = sender;
            _manuAdvRepository = manuAdvRepository;
            _mapper = mapper;
        }
        public async Task<BaseResponse<SalesOrderLineDto>> Handle(GetSalesOrderLineQuery request, CancellationToken cancellationToken)
        {

            var parameters = new List<SqlParameter>()
            { new SqlParameter("SalesOrderLineId", SqlDbType.Int) { Value = request.SalesOrderLineId }
            };

            var resultList = (await _salesOrderLineRepository
                      .SqlQueryRawAsync($"{StoredProcedures.Get_SalesOrderLine} @SalesOrderLineId", parameters.ToArray()));

            var queryResult = resultList.Count > 0 ? resultList[0] : null;

            var response = new BaseResponse<SalesOrderLineDto>() { Success = true };
            if (queryResult != null)
            {
                var user = await _loginRepository.FindAsync(cancellationToken, queryResult.UpdatedBy);
                var companyAdvisoryNotes = await _sender.Send(new GetCompanyAdvisoryNoteQuery()
                {
                    Id = queryResult.CompanyNo
                }, cancellationToken);
                var manuParams = new List<SqlParameter>
                {
                    new SqlParameter("@ManufacturerId", SqlDbType.Int){Value = queryResult.ManufacturerNo ?? 0},
                    new SqlParameter("@ClientID", SqlDbType.Int){Value = request.ClientId},
                };
                var manuNotes = await _manuAdvRepository.SqlQueryRawAsync(
                 $"{StoredProcedures.Get_Manufacturer_Advisory} @ManufacturerId, @ClientID", manuParams.ToArray());
                var salesOrderLine = _mapper.Map<SalesOrderLineDto>(queryResult);
                salesOrderLine.Quantity = Functions.FormatNumeric(queryResult.Quantity, 0, request.CultureInfo);
                salesOrderLine.Price = Functions.FormatCurrency(queryResult.Price, request.CultureInfo, queryResult.CurrencyCode, 5, false);
                salesOrderLine.PriceVal = Functions.FormatCurrency(queryResult.Price, request.CultureInfo, null, 5, false);
                salesOrderLine.QuantityAllocated = Functions.FormatNumeric(queryResult.QuantityAllocated, 0, request.CultureInfo);
                salesOrderLine.QuantityAllocatedNumber = queryResult.QuantityAllocated;
                salesOrderLine.QuantityShipped = Functions.FormatNumeric(queryResult.QuantityShipped, 0, request.CultureInfo);
                salesOrderLine.BackOrderQuantity = Functions.FormatNumeric(queryResult.BackOrderQuantity, 0, request.CultureInfo);
                salesOrderLine.DatePromised = Functions.FormatDate(queryResult.DatePromised, false, false, request.CultureInfo);
                salesOrderLine.RequiredDate = Functions.FormatDate(queryResult.RequiredDate, false, false, request.CultureInfo);
                salesOrderLine.Instructions = Functions.ReplaceLineBreaks(queryResult.Instructions);
                salesOrderLine.ROHSInfo = FormatRoHSHelper.FormatRoHS(queryResult.ROHS);
                salesOrderLine.CompanyOnStop = queryResult.OnStop;
                salesOrderLine.UpdateByName = user?.EmployeeName;
                salesOrderLine.QuantityNumber = queryResult.Quantity;
                salesOrderLine.QuantityShippedNumber = queryResult.QuantityShipped;
                salesOrderLine.CompanyAdvisoryNotes = companyAdvisoryNotes?.Data;
                salesOrderLine.ManufacturerAdvisoryNotes = manuNotes.Count > 0 ? manuNotes[0]?.AdvisoryNotes : null;
                response.Data = salesOrderLine;
            }
            return response;
        }
    }

}


