﻿using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetAddressDefaultBillingForCompany;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetSupplierMessage;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.POLine;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.CurrentAtDate.Queries;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetDefaultBillingForCompany;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Text.Json.Nodes;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetCompanyDetails;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.Queries;

namespace GlobalTrader2.Aggregator.UseCases.Orders.PurchaseOrders.GetPurchaseOrderMainInfo
{
    public class GetPurchaseOrderMainInfoHandler : IRequestHandler<GetPurchaseOrderMainInfoQuery, BaseResponse<PurchaseOrderMainInfoDto>>
    {
        private readonly IBaseRepository<PurchaseOrderDetailsReadModel> _purchaseOrderRepository;
        private readonly IBaseRepository<Login> _loginRepository;
        private readonly IMapper _mapper;
        private readonly ISender _sender;
        public GetPurchaseOrderMainInfoHandler(IBaseRepository<PurchaseOrderDetailsReadModel> purchaseOrderRepository, IMapper mapper, ISender sender, IBaseRepository<Login> loginRepository)
        {
            _purchaseOrderRepository = purchaseOrderRepository;
            _mapper = mapper;
            _sender = sender;
            _loginRepository = loginRepository;
        }

        public async Task<BaseResponse<PurchaseOrderMainInfoDto>> Handle(GetPurchaseOrderMainInfoQuery request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@PurchaseOrderId", request.PurchaseOrderId),
                new SqlParameter("@ClientNo", request.ClientNo),
            };

            var result = await _purchaseOrderRepository.SqlQueryRawAsync(
                $"{StoredProcedures.Select_PurchaseOrder} @PurchaseOrderId, @ClientNo",
                parameters.ToArray());

            if (result == null) throw new ArgumentException($"Purchase order with ID {request.PurchaseOrderId} not found.");

            var purchaseOrderMainInfo = _mapper.Map<PurchaseOrderMainInfoDto>(result[0]);
            var getCompanyAdvisoryNote = await _sender.Send(new GetCompanyAdvisoryNoteQuery { Id = purchaseOrderMainInfo.SupplierNo }, cancellationToken);
            var getDefaultBillingAddress = await _sender.Send(new GetAddressDefaultBillingForCompanyQuery { CompanyId = purchaseOrderMainInfo.SupplierNo }, cancellationToken);
            if (getCompanyAdvisoryNote.Success) purchaseOrderMainInfo.SupplierAdvisoryNotes = getCompanyAdvisoryNote.Data;
            if (getDefaultBillingAddress.Success)
            {
                purchaseOrderMainInfo.BillToAddress = Functions.ReplaceLineBreaks(getDefaultBillingAddress.Data?.ToLongString() ?? string.Empty);
                purchaseOrderMainInfo.BillToAddressId = getDefaultBillingAddress.Data?.AddressId;
                purchaseOrderMainInfo.DefaultIncotermNo = getDefaultBillingAddress.Data?.IncotermNo;
                purchaseOrderMainInfo.DefaultIncoterm = getDefaultBillingAddress.Data?.IncotermName;
            }
            purchaseOrderMainInfo.Currency = Functions.FormatCurrencyDescription(purchaseOrderMainInfo.CurrencyDescription, purchaseOrderMainInfo.CurrencyCode);
            var warningMessage = await _sender.Send(new GetPoLineWarningMessageQuery(purchaseOrderMainInfo.ImportCountryNo, request.ClientNo), cancellationToken);
            if (warningMessage.Success && warningMessage.Data != null)
            {
                purchaseOrderMainInfo.WarningMessage = warningMessage.Data.WarningMessage;
                purchaseOrderMainInfo.IsHighRisk = warningMessage.Data.HighRisk;
            }
            else
            {
                purchaseOrderMainInfo.WarningMessage = string.Empty;
                purchaseOrderMainInfo.IsHighRisk = false;
            }
            purchaseOrderMainInfo.IsPOHub = Convert.ToBoolean(request.IsPOHub);
            var strTotalShipInCost = Functions.FormatCurrency(purchaseOrderMainInfo.TotalShipInCost, CultureInfo.CurrentCulture, request.ClientCurrencyCode, 2, true);
            if (purchaseOrderMainInfo.CurrencyNo != request.ClientCurrencyID)
            {
                var baseCurrency = await ConvertValueFromBaseCurrency(Convert.ToDecimal(purchaseOrderMainInfo.TotalShipInCost), purchaseOrderMainInfo.CurrencyNo, purchaseOrderMainInfo.DateOrderedRaw);
                strTotalShipInCost += string.Format(" ({0})", Functions.FormatCurrency(baseCurrency, CultureInfo.CurrentCulture, purchaseOrderMainInfo.CurrencyCode, 2, true));
            }

            purchaseOrderMainInfo.TotalShipInCostVal = Functions.FormatCurrency(purchaseOrderMainInfo.TotalShipInCost,CultureInfo.CurrentCulture, null, 2, false);
            purchaseOrderMainInfo.TotalShipInCost = strTotalShipInCost;


            purchaseOrderMainInfo.SupplierRMAIds = GetJsonList(result[0].SupplierRMAIds, "SupplierRMAId");
            purchaseOrderMainInfo.SupplierRMANumbers = GetJsonList(result[0].SupplierRMANumbers, "SupplierRMANumber");
            purchaseOrderMainInfo.DebitIds = GetJsonList(result[0].DebitIds, "DebitId");
            purchaseOrderMainInfo.DebitNumbers = GetJsonList(result[0].DebitNumbers, "DebitNumber");
            purchaseOrderMainInfo.EPRIds = GetJsonList(result[0].EPRIds, "EPRId");
            purchaseOrderMainInfo.POLineEPRIds = GetJsonList(result[0].POLineEPRIds, "POLineEPRId");

            purchaseOrderMainInfo.IsIPO = purchaseOrderMainInfo.InternalPurchaseOrderNo.HasValue && purchaseOrderMainInfo.InternalPurchaseOrderNo.Value > 0;
            purchaseOrderMainInfo.IsIPOApproved = purchaseOrderMainInfo.ApprovedBy.HasValue && purchaseOrderMainInfo.ApprovedBy > 0;
            purchaseOrderMainInfo.GBLWhrClientNo = purchaseOrderMainInfo.InternalPurchaseOrderNo.HasValue && purchaseOrderMainInfo.InternalPurchaseOrderNo.Value > 0 ? purchaseOrderMainInfo.IPOClientNo : purchaseOrderMainInfo.ClientNo;
            
            var supplierStatusMessage = await _sender.Send(new GetSupplierMessageQuery(purchaseOrderMainInfo.SupplierNo), cancellationToken);
            purchaseOrderMainInfo.SupplierMessage = supplierStatusMessage.Data != null ? 
                Functions.ReplaceLineBreaks(supplierStatusMessage.Data) : string.Empty;

            var shipToAddress = await _sender.Send(new GetDefaultBillingForCompanyQuery(purchaseOrderMainInfo.SupplierNo), cancellationToken);
            purchaseOrderMainInfo.ShipToAddressId = shipToAddress.Data != null ? shipToAddress.Data.AddressId : 0;
            if (purchaseOrderMainInfo.UpdatedBy != null)
            {
                var login = await _loginRepository.FindAsync(cancellationToken, purchaseOrderMainInfo.UpdatedBy);
                purchaseOrderMainInfo.UpdatedByName = login?.EmployeeName ?? string.Empty;
            }
     
            var response = new BaseResponse<PurchaseOrderMainInfoDto>
            {
                Data = purchaseOrderMainInfo,
                Success = true
            };
            return response;
        }

        private static List<JsonObject> GetJsonList(string? delimitedValues, string propertyName, char delimiter = ',')
        {
            List<JsonObject> jsonList = new List<JsonObject>();

            if (string.IsNullOrEmpty(delimitedValues))
            {
                return new List<JsonObject>();
            }

            var values = delimitedValues.Split(delimiter, StringSplitOptions.RemoveEmptyEntries);
            foreach (var value in values)
            {
                var trimmedValue = value.Trim();
                if (!string.IsNullOrEmpty(trimmedValue))
                {
                    var jsonObject = new JsonObject
                    {
                        [propertyName] = JsonValue.Create(trimmedValue)
                    };
                    jsonList.Add(jsonObject);
                }
            }
            return jsonList;
        }

        public async Task<decimal> ConvertValueFromBaseCurrency(decimal? dblValueToConvert, int currencyNo, DateTime? dtmExchangeRateDate)
        {
            DateTime datePoint = dtmExchangeRateDate == null ? DateTime.MinValue : dtmExchangeRateDate.Value;
            var dblRate = await _sender.Send(new GetCurrentAtDateQuery()
            {
                CurrencyNo = currencyNo,
                DatePoint = datePoint,
            }, CancellationToken.None);
            var dblValueToConvert_AsDecimal = dblValueToConvert ?? 0;
            return dblValueToConvert_AsDecimal * Convert.ToDecimal(dblRate.Data!.ExchangeRate);
        }
    }   
}
