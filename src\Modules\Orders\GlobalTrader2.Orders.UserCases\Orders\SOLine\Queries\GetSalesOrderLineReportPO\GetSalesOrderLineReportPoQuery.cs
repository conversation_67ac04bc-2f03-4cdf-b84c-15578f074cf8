﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.SalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderLineReportPO
{
    public class GetSalesOrderLineReportPoQuery : IRequest<BaseResponse<IReadOnlyList<SalesOrderLineReportPoDto>>>
    {
        public int SalesOrderLineId { get; set; }
        public GetSalesOrderLineReportPoQuery(int SalesOrderLineId)
        {
            this.SalesOrderLineId = SalesOrderLineId;
        }
    }
}
