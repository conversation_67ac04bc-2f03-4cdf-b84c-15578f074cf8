import { SalesOrderDetailApiUrl, SalesOrderLineApiUrl } from '../../../../../../config/api-endpoint-config.js?v=#{BuildVersion}#';
import { LinesLevelConfig } from './configs/lines-level.config.js?v=#{BuildVersion}#';
export class LinesTabService {
    static #baseURL = SalesOrderDetailApiUrl;

    static async getLinesTabDatatableAsync(salesOrderId, level) {
        switch (level) {
            case LinesLevelConfig.ALL:
                return await this.getAllLinesDatatableAsync(salesOrderId);
            case LinesLevelConfig.OPEN:
                return await this.getOpenLinesDatatableAsync(salesOrderId);
            case LinesLevelConfig.CLOSED:
                return await this.getClosedLinesDatatableAsync(salesOrderId);
            default:
                return null;
        }
    }
    
    static async getAllLinesDatatableAsync(salesOrderId) {
        return await GlobalTrader.ApiClient.getAsync(`${this.#baseURL}/${salesOrderId}/all-so-lines`);
    }
    
    static async getOpenLinesDatatableAsync(salesOrderId) {
        return await GlobalTrader.ApiClient.getAsync(`${this.#baseURL}/${salesOrderId}/open-so-lines`);
    }
    
    static async getClosedLinesDatatableAsync(salesOrderId) {
        return await GlobalTrader.ApiClient.getAsync(`${this.#baseURL}/${salesOrderId}/closed-so-lines`);
    }

    static async getLineDetailsAsync(salesOrderLineId) {
        return await GlobalTrader.ApiClient.getAsync(`${SalesOrderLineApiUrl}/${salesOrderLineId}`);
    }

    static getPromiseLogUrl(salesOrderLineId) {
        return `/api/orders/sales-order/so-lines/${salesOrderLineId}/promise-reason-logs`;
    }
    
    static async getAS6081MessageAsync(operationType) {
        const param = new URLSearchParams({operationType: operationType});
        return await GlobalTrader.ApiClient.getAsync(`/as6081/alert-message?${param}`);
    }

    static async getIHSEccnCodeByEccnCodeAsync(eccnCode) {
        return await GlobalTrader.ApiClient.getAsync('/orders/customer-requirements/get-ihs-eccn-code-detail', {
            eccnCode: eccnCode
        });
    }

    static async getIHSEccnCodeByPartNoAsync(partNo) {
        return await GlobalTrader.ApiClient.postAsync(`${SalesOrderLineApiUrl}/get-so-ihs-eccn-detail`, {
            partNo: partNo
        });
    }    

    static async updateSalesOrderLineAsync(salesOrderLineId, data, header) {
        return await GlobalTrader.ApiClient.putAsync(`${SalesOrderLineApiUrl}/${salesOrderLineId}`, data, header);
    }

    static async notifyEccnSalesAsync(salesOrderId, salesOrderLineId) {
        return await GlobalTrader.ApiClient.postAsync(`${SalesOrderLineApiUrl}/eccn-sales-notification`, {
            SaleOrderId: salesOrderId,
            SaleOrderLineId: salesOrderLineId
        });
    }

}
