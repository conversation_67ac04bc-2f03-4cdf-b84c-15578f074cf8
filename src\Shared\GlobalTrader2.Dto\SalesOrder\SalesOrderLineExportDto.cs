namespace GlobalTrader2.Dto.SalesOrder;
public class SalesOrderLineExportDto
{
    public int SalesOrderNo { get; set; }
    public string? PartNo { get; set; } 
    public string CustomerPoNo { get; set; } = string.Empty;
    public int QtyOrdered { get; set; }
    public int QtyShipped { get; set; }
    public int QtyInStock { get; set; }
    public string Company { get; set; } = string.Empty;
    public string Contact { get; set; } = string.Empty;
    public string Ordered { get; set; } = string.Empty;
    public string? Promised { get; set; } 
    public string Status { get; set; } = string.Empty;
    public int? ContractNo { get; set; }
}
