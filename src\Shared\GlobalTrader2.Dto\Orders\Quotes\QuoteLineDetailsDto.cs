namespace GlobalTrader2.Dto.Orders.Quotes
{
    public class QuoteLineDetailsDto
    {
        public int QuoteLineId { get; set; }

        public int QuoteNo { get; set; }

        public string? FullPart { get; set; }

        public string? Part { get; set; }

        public int? ManufacturerNo { get; set; }

        public string? DateCode { get; set; }

        public int? PackageNo { get; set; }

        public int Quantity { get; set; }

        public double? Price { get; set; }

        public string? ETA { get; set; }

        public string? Instructions { get; set; }

        public int? ProductNo { get; set; }

        public int? ReasonNo { get; set; }

        public string? CustomerPart { get; set; }

        public int? StockNo { get; set; }

        public byte? ROHS { get; set; }

        public bool Closed { get; set; }

        public int? ServiceNo { get; set; }

        public double? OriginalOfferPrice { get; set; }

        public int? OriginalOfferCurrencyNo { get; set; }

        public DateTime? OriginalOfferDate { get; set; }

        public string? LineNotes { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime DLUP { get; set; }

        public string? ManufacturerName { get; set; }

        public string? ManufacturerCode { get; set; }

        public string? PackageName { get; set; }

        public string? PackageDescription { get; set; }

        public string? ProductName { get; set; }

        public string? ProductDescription { get; set; }

        public string? CurrencyCode { get; set; }

        public int? CurrencyNo { get; set; }

        public int? QuoteNumber { get; set; }

        public int CompanyNo { get; set; }

        public string? CompanyName { get; set; }

        public DateTime DateQuoted { get; set; }

        public string? ReasonName { get; set; }

        public string? OriginalOfferCurrencyCode { get; set; }

        public bool NotQuoted { get; set; }

        public int? OriginalOfferSupplierNo { get; set; }

        public string? OriginalOfferSupplierName { get; set; }

        public int? SourcingResultNo { get; set; }

        public int? ClientNo { get; set; }

        public int? IncotermNo { get; set; }

        public byte? ProductSource { get; set; }

        public string? SourcingTable { get; set; }

        public int? POHubCompanyNo { get; set; }

        public DateTime? DeliveryDate { get; set; }

        public bool ProductInactive { get; set; } //bool

        public string? Notes { get; set; }

        public string? DutyCode { get; set; }

        public bool IsProdHazardous { get; set; }

        public bool? PrintHazardous { get; set; }

        public string? MSLLevel { get; set; }

        public string? CountryOfOrigin { get; set; }

        public string? IHSCountryOfOrigin { get; set; }

        public string? LifeCycleStage { get; set; }

        public string? HTSCode { get; set; }

        public double? AveragePrice { get; set; }

        public string? Packing { get; set; }

        public string? PackagingSize { get; set; }

        public string? Descriptions { get; set; }

        public string? IHSProduct { get; set; }

        public string? ECCNCode { get; set; }

        public bool IsOrderViaIPOonly { get; set; } //bool

        public bool IsRestrictedProduct { get; set; }//bool

        public bool AS6081 { get; set; }

        public int? ProductSourceId { get; set; }

        public string? Name { get; set; }

        public int? DisplaySortOrder { get; set; }

        public int? IsIPOCreated { get; set; }

        public double? ProductDutyRate { get; set; }

        public string? ProductSourceName { get; set; }

        public bool IsRestManufaturer { get; set; }

        public string? IHSECCNCodeDefination { get; set; }

        public string? StockAvailableDetail { get; set; }
    }
}