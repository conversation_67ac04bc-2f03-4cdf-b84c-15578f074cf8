using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSoLineExportData;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SalesOrders.Queries;
public class GetSoLineExportDataHandlerTests
{
    private readonly IFixture _fixture;
    private readonly GetSoLineExportDataHandler _getSoLineExportDataHandler;
    private readonly Mock<IBaseRepository<SalesOrderLineExportReadModel>> _repo;
    private readonly Mock<IMapper> _mapperMock;
    public GetSoLineExportDataHandlerTests()
    {
        _fixture = new Fixture().Customize(new AutoMoqCustomization());
        _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        _mapperMock = new Mock<IMapper>();
        _repo = _fixture.Freeze<Mock<IBaseRepository<SalesOrderLineExportReadModel>>>();
        _getSoLineExportDataHandler = new GetSoLineExportDataHandler(_repo.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task HandleSearchSalesOrdersHandlerTest_Success()
    {
        // Arrange
        var query = _fixture.Create<GetSoLineExportDataQuery>();
        var salesOrderLineList = _fixture.Create<IReadOnlyList<SalesOrderLineExportReadModel>>();
        var salesOrderLineDtos = _fixture.Create<List<SalesOrderLineExportDto>>();

        _repo.Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>(), It.IsAny<int>())).ReturnsAsync(salesOrderLineList);
        _mapperMock.Setup(mapper => mapper.Map<IEnumerable<SalesOrderLineExportDto>>(salesOrderLineList)).Returns(salesOrderLineDtos);

        // Act
        var response = await _getSoLineExportDataHandler.Handle(query, CancellationToken.None);

        //Assert
        Assert.True(response.Success);
        Assert.Equal(salesOrderLineDtos, response.Data);
    }
}