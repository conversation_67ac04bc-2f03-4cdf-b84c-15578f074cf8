﻿using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.CollapsibleFieldSetLite;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details.Components.Allocations
{
    public class Allocations : ViewComponent
    {
        public Task<IViewComponentResult> InvokeAsync(CommonComponentParams model)
        {
            return Task.FromResult<IViewComponentResult>(View(model));
        }
    }
}
